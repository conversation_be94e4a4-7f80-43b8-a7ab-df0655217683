package jp.co.torihada.fanme.modules.fanme.usecases.contentBlock

import com.github.michaelbull.result.unwrap
import io.quarkus.test.TestTransaction
import io.quarkus.test.junit.QuarkusTest
import jakarta.inject.Inject
import jp.co.torihada.fanme.Const
import jp.co.torihada.fanme.modules.fanme.factories.ContentBlockDetailFactory
import jp.co.torihada.fanme.modules.fanme.factories.ContentBlockFactory
import jp.co.torihada.fanme.modules.fanme.factories.ContentBlockGroupFactory
import jp.co.torihada.fanme.modules.fanme.factories.UserFactory
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test

@QuarkusTest
class GetContentBlocksTest {

    @Inject lateinit var getContentBlocks: GetContentBlocks

    @Test
    @TestTransaction
    fun `test get content blocks for user with no content blocks`() {
        val user =
            UserFactory.createTestUser(
                uid = "test-user-uid",
                name = "Test User",
                accountIdentity = "test-user-account-identity",
            ) ?: throw IllegalStateException("Failed to create test user")
        user.persistAndFlush()

        val input = GetContentBlocks.Input(user = user, applyMasking = false)
        val result = getContentBlocks.execute(input).unwrap()

        assertEquals(0, result.size)
    }

    @Test
    @TestTransaction
    fun `test get content blocks for user`() {
        val user =
            UserFactory.createTestUser(
                uid = "test-user-uid",
                name = "Test User",
                accountIdentity = "test-user-account-identity",
            ) ?: throw IllegalStateException("Failed to create test user")
        user.persistAndFlush()

        val contentBlock =
            ContentBlockFactory.new(
                user = user,
                contentBlockTypeId = Const.ContentBlockType.TWO_BLOCKS.id,
                displayOrderNumber = 1,
                displayable = true,
            )
        val contentBlockDetail1 = ContentBlockDetailFactory.new()
        val contentBlockGroup1 =
            ContentBlockGroupFactory.new(
                contentBlock = contentBlock,
                contentBlockDetail = contentBlockDetail1,
                contentGroupNumber = 1,
            )
        val contentBlockDetail2 =
            ContentBlockDetailFactory.new(icon = "https://example.com/icon.png")
        val contentBlockGroup2 =
            ContentBlockGroupFactory.new(
                contentBlock = contentBlock,
                contentBlockDetail = contentBlockDetail2,
                contentGroupNumber = 2,
            )

        contentBlock.persistAndFlush()
        contentBlockDetail1.persistAndFlush()
        contentBlockDetail2.persistAndFlush()

        contentBlockGroup1.persistAndFlush()
        contentBlockGroup2.persistAndFlush()

        contentBlock.contentBlockGroups.add(contentBlockGroup1)
        contentBlock.contentBlockGroups.add(contentBlockGroup2)

        contentBlock.persistAndFlush()

        val input = GetContentBlocks.Input(user = user, applyMasking = false)
        val result = getContentBlocks.execute(input).unwrap()

        assertEquals(1, result.size)
        assertEquals(2, result[0].contentBlockDetails.size)
        assertEquals(Const.ContentBlockType.TWO_BLOCKS.id, result[0].contentBlockType)
        assertEquals(1, result[0].displayOrderNumber)
        assertEquals(true, result[0].displayable)
        assertEquals(false, result[0].contentBlockDetails[0].isSetIcon)
        assertEquals(true, result[0].contentBlockDetails[1].isSetIcon)
        assertEquals(1, result[0].contentBlockDetails[0].contentGroupNumber)
        assertEquals(2, result[0].contentBlockDetails[1].contentGroupNumber)
    }
}
