package jp.co.torihada.fanme.modules.fanme.controllers

import io.quarkus.test.TestTransaction
import io.quarkus.test.junit.QuarkusTest
import jakarta.inject.Inject
import jp.co.torihada.fanme.exception.ResourceNotFoundException
import jp.co.torihada.fanme.modules.fanme.factories.ContentBlockFactory
import jp.co.torihada.fanme.modules.fanme.factories.UserFactory
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows

@QuarkusTest
class ContentBlockControllerTest {

    @Inject lateinit var contentBlockController: ContentBlockController

    @Test
    @TestTransaction
    fun `test getUserContentBlocks with non-existent user`() {
        assertThrows<ResourceNotFoundException> {
            contentBlockController.getUserContentBlocks(
                creatorAccountIdentity = "non-existent-user",
                applyMasking = false,
            )
        }
    }

    @Test
    @TestTransaction
    fun `test getCurrentUserContentBlocks with non-existent user`() {
        assertThrows<ResourceNotFoundException> {
            contentBlockController.getCurrentUserContentBlocks(userUid = "non-existent-user-uid")
        }
    }

    @Test
    @TestTransaction
    fun `test getUserContentBlocks with user`() {
        val user =
            UserFactory.createTestUser(
                uid = "test-user-uid",
                name = "Test User",
                accountIdentity = "test-user-account-identity",
            ) ?: throw IllegalStateException("Failed to create test user")
        user.persistAndFlush()

        val contentBlock1 = ContentBlockFactory.new(user = user, displayOrderNumber = 1)
        val contentBlock2 = ContentBlockFactory.new(user = user, displayOrderNumber = 2)
        contentBlock1.persist()
        contentBlock2.persist()

        val result =
            contentBlockController.getUserContentBlocks(
                creatorAccountIdentity = user.accountIdentity!!,
                applyMasking = false,
            )

        assert(result.isNotEmpty()) { "Content blocks should not be empty" }
        assert(result.size == 2) { "Should return two content blocks" }
        assert(result[0].displayOrderNumber == 1) {
            "First content block should have display order 1"
        }
        assert(result[1].displayOrderNumber == 2) {
            "Second content block should have display order 2"
        }
    }

    @Test
    @TestTransaction
    fun `test getCurrentUserContentBlocks with user`() {
        val user =
            UserFactory.createTestUser(
                uid = "test-user-uid",
                name = "Test User",
                accountIdentity = "test-user-account-identity",
            ) ?: throw IllegalStateException("Failed to create test user")
        user.persistAndFlush()

        val contentBlock1 = ContentBlockFactory.new(user = user, displayOrderNumber = 1)
        val contentBlock2 = ContentBlockFactory.new(user = user, displayOrderNumber = 2)
        contentBlock1.persist()
        contentBlock2.persist()

        val result = contentBlockController.getCurrentUserContentBlocks(userUid = user.uid!!)

        assert(result.isNotEmpty()) { "Content blocks should not be empty" }
        assert(result.size == 2) { "Should return two content blocks" }
        assert(result[0].displayOrderNumber == 1) {
            "First content block should have display order 1"
        }
        assert(result[1].displayOrderNumber == 2) {
            "Second content block should have display order 2"
        }
    }
}
