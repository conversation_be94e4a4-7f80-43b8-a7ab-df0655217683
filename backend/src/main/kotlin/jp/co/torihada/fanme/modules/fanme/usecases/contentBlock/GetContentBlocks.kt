package jp.co.torihada.fanme.modules.fanme.usecases.contentBlock

import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import jakarta.enterprise.context.ApplicationScoped
import jp.co.torihada.fanme.annotations.Maskable
import jp.co.torihada.fanme.lib.MaskingProcessor
import jp.co.torihada.fanme.modules.fanme.models.ContentBlock as ContentBlockModel
import jp.co.torihada.fanme.modules.fanme.models.User
import org.eclipse.microprofile.openapi.annotations.media.Schema

@ApplicationScoped
class GetContentBlocks {

    class Input(val user: User, val applyMasking: Boolean)

    @Schema(name = "GetContentBlock_ContentBlock")
    data class ContentBlock(
        val id: Long,
        val contentBlockDetails: List<ContentBlockDetail>,
        val contentBlockType: Long,
        val displayOrderNumber: Int,
        val displayable: <PERSON>olean,
    ) {
        @Schema(name = "GetContentBlock_ContentBlockDetail")
        data class ContentBlockDetail(
            val id: Long,
            val contentGroupNumber: Int,
            val isSetIcon: Boolean,
            @Maskable val title: String,
            val url: String?,
            @Maskable val description: String?,
            @Maskable val appDescription: String?,
            val icon: String?,
            val style: MutableMap<String, Any>?,
        )
    }

    fun execute(input: Input): Result<List<ContentBlock>, Exception> {
        val contentBlocks = ContentBlockModel.findByUser(input.user)

        val integrated =
            contentBlocks.map { contentBlock ->
                ContentBlock(
                    id = contentBlock.id!!,
                    contentBlockDetails =
                        contentBlock.contentBlockGroups.map { group ->
                            val detail = group.contentBlockDetail
                            ContentBlock.ContentBlockDetail(
                                id = detail?.id!!,
                                appDescription = detail.appDescription,
                                contentGroupNumber = group.contentGroupNumber!!,
                                description = detail.description,
                                icon = detail.iconUrl,
                                isSetIcon = !detail.icon.isNullOrEmpty(),
                                style = detail.style,
                                title = detail.title ?: "",
                                url = detail.url,
                            )
                        },
                    contentBlockType = contentBlock.contentBlockTypeId!!,
                    displayOrderNumber = contentBlock.displayOrderNumber ?: 0,
                    displayable = contentBlock.displayable ?: false,
                )
            }

        val result = if (input.applyMasking) MaskingProcessor.process(integrated) else integrated

        return Ok(result)
    }
}
