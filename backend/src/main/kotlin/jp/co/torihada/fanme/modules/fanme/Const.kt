package jp.co.torihada.fanme.modules.fanme

object Const {

    const val TENANT = "fanme"
    const val FANME_DEFAULT_USER_NAME = "FANMEユーザー"
    const val FANME_DEFAULT_CREATOR_ICON_PATH = "images/creator_default_icon.svg"
    const val FANME_DEFAULT_CONTENT_ICON_PATH = "images/content_block_icon_none.svg"
    const val S3_PATH = "uploads"

    const val USER_UID_MAX_LENGTH = 50
    const val CONTENT_TITLE_MAX_LENGTH = 50
    const val CONTENT_DESCRIPTION_MAX_LENGTH = 500

    enum class Gender(val value: String) {
        Male("MALE"),
        Female("FEMALE"),
        Other("OTHER"),
        BLANK("BLANK");

        companion object {
            fun fromValue(value: String): Gender? {
                return entries.firstOrNull { it.value == value }
            }
        }
    }

    enum class Purpose(val value: Int) {
        Creator(1),
        Fan(2),
        Unset(0);

        companion object {
            fun fromType(value: Int): Purpose? {
                return entries.firstOrNull { it.value == value }
            }
        }
    }

    enum class SnsLinkColor(val value: String) {
        ORIGINAL("ORIG"),
        MONOCHROME("MONO"),
    }

    enum class ThemeColor(val value: Long, val str: String? = null) {
        DARK(1, "Dark"),
        LIGHT(2, "Light"),
        CUSTOM(3, "Custom");

        companion object {
            fun fromValue(value: Long): ThemeColor? {
                return ThemeColor.entries.firstOrNull { it.value == value }
            }
        }
    }

    enum class Brightness(val value: String) {
        DARK("dark"),
        NORMAL("normal"),
        LIGHT("light"),
    }

    enum class CoverResourceType(val value: String) {
        PHOTO("photo"),
        VIDEO("video"),
    }
}
