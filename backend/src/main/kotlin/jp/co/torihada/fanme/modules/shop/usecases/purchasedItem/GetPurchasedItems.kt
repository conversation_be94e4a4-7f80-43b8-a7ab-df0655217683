package jp.co.torihada.fanme.modules.shop.usecases.purchasedItem

import com.fasterxml.jackson.databind.PropertyNamingStrategies
import com.fasterxml.jackson.databind.annotation.JsonNaming
import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jp.co.torihada.fanme.Const
import jp.co.torihada.fanme.annotations.Maskable
import jp.co.torihada.fanme.exception.FanmeException
import jp.co.torihada.fanme.lib.MaskingProcessor
import jp.co.torihada.fanme.modules.fanme.controllers.UserController
import jp.co.torihada.fanme.modules.payment.Const as PaymentConst
import jp.co.torihada.fanme.modules.payment.controllers.CheckoutController
import jp.co.torihada.fanme.modules.payment.controllers.TipController
import jp.co.torihada.fanme.modules.payment.models.Tip
import jp.co.torihada.fanme.modules.shop.Config
import jp.co.torihada.fanme.modules.shop.Util
import jp.co.torihada.fanme.modules.shop.models.ItemType
import jp.co.torihada.fanme.modules.shop.models.Order
import jp.co.torihada.fanme.modules.shop.models.PurchasedItem as PurchasedItemModel
import jp.co.torihada.fanme.odata.OData

@ApplicationScoped
class GetPurchasedItems {

    @Inject private lateinit var config: Config

    @Inject private lateinit var userController: UserController
    @Inject private lateinit var checkoutController: CheckoutController
    @Inject private lateinit var tipController: TipController

    data class Input(
        val userUid: String,
        val includeTip: Boolean,
        val odata: OData?,
        val applyMasking: Boolean,
    )

    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
    data class PurchasedItem(
        val id: Long,
        val tipId: Long?,
        val orderId: Long,
        val orderNumber: String,
        @Maskable val name: String,
        @Maskable val description: String,
        val thumbnailUri: String,
        val viewerUrl: String,
        val isValid: Boolean = true,
        val price: Int,
        val itemType: Int,
        val purchasedAt: String,
        val creatorUid: String,
        @Maskable val creatorName: String,
        val creatorIcon: String,
        val shopHeaderImageUri: String,
        val checkout: Checkout?,
    )

    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
    data class Checkout(
        val convenience: String?,
        val confNo: String?,
        val receiptNo: String?,
        val paymentTerm: String?,
        val receiptUrl: String?,
        val status: String?,
        val total: Int?,
    )

    fun execute(input: Input): Result<List<PurchasedItem>, FanmeException> {
        val purchasedItems =
            PurchasedItemModel.findValidItemsByPurchaserUid(
                input.userUid,
                input.odata?.top,
                input.odata?.skip,
            )

        val checkoutIds =
            purchasedItems.map { it.order.checkoutId }.toSet().toList().filterNotNull()
        val checkouts = checkoutController.getCheckouts(checkoutIds)

        val filteredItems =
            purchasedItems
                .mapNotNull { item ->
                    val checkout = checkouts.find { it.id == item.order.checkoutId }

                    // checkoutがnullの場合やPayPay未払いの場合は除外
                    if (checkout != null) {
                        if (
                            checkout.type == PaymentConst.PaymentType.PAY_PAY.value &&
                                checkout.status != Const.CheckoutStatus.PAYSUCCESS.value
                        ) {
                            null
                        } else {
                            item
                        }
                    } else {
                        null
                    }
                }
                .toMutableList()

        val creatorUIds =
            filteredItems.map { it.item.shop.creatorUid }.toSet().toList().filterNotNull()
        val creators = userController.getUsers(creatorUIds)
        val items =
            filteredItems
                .map { purchasedItem ->
                    val creator = creators.find { it.uid == purchasedItem.item.shop.creatorUid }
                    if (creator == null) return@map null
                    val checkout = checkouts.find { it.id == purchasedItem.order.checkoutId }

                    PurchasedItem(
                        id = purchasedItem.id!!,
                        tipId = null,
                        orderId = purchasedItem.order.id!!,
                        orderNumber = Order.generateOrderNumber(purchasedItem.order.id!!),
                        name =
                            if (purchasedItem.itemFile != null) {
                                purchasedItem.itemFile!!.name
                            } else {
                                purchasedItem.item.name
                            },
                        description =
                            if (purchasedItem.itemFile != null) {
                                ""
                            } else {
                                purchasedItem.item.description ?: ""
                            },
                        thumbnailUri = purchasedItem.item.thumbnailUri,
                        // 将来的に全て購入詳細ページに遷移するようにする
                        viewerUrl =
                            // ビュワー -> 購入詳細ページに移行する際、listにタイプを追加する
                            if (purchasedItem.item.itemType in listOf(ItemType.CHEKI)) {
                                "${config.shopFrontUrl()}/@${creator.accountIdentity}/purchased-item/${purchasedItem.id}"
                            } else if (
                                purchasedItem.item.itemType in listOf(ItemType.DIGITAL_GACHA)
                            ) {
                                "${config.shopFrontUrl()}/@${creator.accountIdentity}/item/${purchasedItem.item.id}"
                            } else {
                                "${config.shopFrontUrl()}/@${creator.accountIdentity}/viewer/${purchasedItem.item.id}"
                            },
                        isValid = purchasedItem.status == Util.PurchasedItemStatus.PAYSUCCESS.value,
                        price =
                            if (purchasedItem.itemFile != null) {
                                purchasedItem.itemFile!!.price ?: 0
                            } else {
                                purchasedItem.price
                            },
                        itemType = purchasedItem.item.itemType.value,
                        purchasedAt = Util.toJSTLocalDateTimeString(purchasedItem.createdAt!!),
                        creatorUid = creator.uid ?: "",
                        creatorName = creator.name ?: "FANMEユーザー",
                        creatorIcon = creator.iconUrl,
                        shopHeaderImageUri = purchasedItem.item.shop.headerImageUri ?: "",
                        checkout =
                            Checkout(
                                convenience = checkout?.convenience,
                                confNo = checkout?.confNo,
                                receiptNo = checkout?.receiptNo,
                                paymentTerm =
                                    if (checkout?.paymentTerm != null) {
                                        Util.toDateTimeString(checkout.paymentTerm!!)
                                    } else {
                                        null
                                    },
                                receiptUrl = checkout?.receiptUrl,
                                status = checkout?.status,
                                total = checkout?.total,
                            ),
                    )
                }
                .filterNotNull()
                .toMutableList()

        // 購入履歴の場合はチップも含める
        var tips: List<Tip> = mutableListOf()
        val shops = filteredItems.map { it.item.shop }.toSet().toList()
        val orders = filteredItems.map { it.order }.toSet().toList()
        if (input.includeTip) {
            val transactionIds =
                purchasedItems.map { it.order.transactionId }.toSet().toList().filterNotNull()
            tips = tipController.getTransactionsTip(transactionIds)
        }
        if (tips.isNotEmpty()) {
            tips.forEach { tip ->
                val creator = creators.find { it.uid == tip.sellerUserId }
                val shop = shops.find { it.creatorUid == tip.sellerUserId }
                val order = orders.find { it.transactionId == tip.transaction?.id }
                if (creator == null || shop == null || order == null) return@forEach
                items.add(
                    PurchasedItem(
                        id = -1,
                        tipId = tip.id,
                        orderId = order.id!!,
                        orderNumber = Order.generateOrderNumber(order.id!!),
                        name = "チップ",
                        description = "",
                        thumbnailUri = "",
                        viewerUrl = "",
                        isValid = true,
                        price = tip.amount ?: 0,
                        // チップ用のダミータイプ
                        itemType = -1,
                        purchasedAt = Util.toJSTLocalDateTimeString(tip.createdAt!!),
                        creatorUid = creator.uid ?: "",
                        creatorName = creator.name ?: "FANMEユーザー",
                        creatorIcon = creator.iconUrl,
                        shopHeaderImageUri = shop.headerImageUri ?: "",
                        checkout = null,
                    )
                )
            }
        }

        // 購入日時の降順、サムネイルあり優先(チップが後になる)でソート
        items.sortWith(
            compareByDescending<PurchasedItem> { it.purchasedAt }
                .thenByDescending { it.thumbnailUri.isNotEmpty() }
        )

        val result = if (input.applyMasking) items.map { MaskingProcessor.process(it) } else items

        return Ok(result)
    }
}
