package jp.co.torihada.fanme.modules.shop.usecases.purchasedItem

import com.fasterxml.jackson.databind.PropertyNamingStrategies
import com.fasterxml.jackson.databind.annotation.JsonNaming
import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jp.co.torihada.fanme.annotations.Maskable
import jp.co.torihada.fanme.exception.FanmeException
import jp.co.torihada.fanme.exception.NotOwnerException
import jp.co.torihada.fanme.exception.ResourceNotFoundException
import jp.co.torihada.fanme.lib.MaskingProcessor
import jp.co.torihada.fanme.modules.payment.controllers.CheckoutController
import jp.co.torihada.fanme.modules.shop.Util
import jp.co.torihada.fanme.modules.shop.models.Order
import jp.co.torihada.fanme.modules.shop.models.PurchasedItem as PurchasedItemModel

@ApplicationScoped
class GetPurchasedItem {

    @Inject private lateinit var checkoutController: CheckoutController

    data class Input(val userUid: String, val purchasedItemId: Long, val applyMasking: Boolean)

    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
    data class PurchasedItemDetail(
        val id: Long,
        val itemId: Long,
        val purchasedAt: String,
        val order: PurchasedItemOrder,
        val checkout: PurchasedItemCheckout?,
        val purchaserComment: String?,
    ) {
        @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
        data class PurchasedItemOrder(
            val id: Long,
            val orderNumber: String,
            val items: List<OrderedItem>,
            val orderedAt: String,
        ) {
            data class OrderedItem(
                val id: Long,
                @Maskable val name: String,
                val itemType: Int,
                val price: Int,
                val marginRate: Float,
                val quantity: Int,
            )
        }

        @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
        data class PurchasedItemCheckout(
            val paymentType: String?,
            val convenience: String?,
            val confNo: String?,
            val receiptNo: String?,
            val paymentTerm: String?,
            val receiptUrl: String?,
            val status: String?,
            val total: Int?,
            val tipAmount: Int?,
            val cvsFee: Int?,
            val deliveryFee: Int?,
        )
    }

    fun execute(input: Input): Result<PurchasedItemDetail, FanmeException> {
        val purchasedItem =
            PurchasedItemModel.findById(input.purchasedItemId)
                ?: throw ResourceNotFoundException("PurchasedItem")

        if (purchasedItem.purchaserUid != input.userUid) {
            throw NotOwnerException("PurchasedItem")
        }

        val checkoutId =
            purchasedItem.order.checkoutId ?: throw ResourceNotFoundException("CheckoutId")

        val checkout = checkoutController.getCheckout(checkoutId)

        val itemDetails =
            PurchasedItemDetail(
                id = purchasedItem.id!!,
                itemId = purchasedItem.item.id!!,
                purchasedAt = Util.toJSTLocalDateTimeString(purchasedItem.createdAt!!),
                order =
                    PurchasedItemDetail.PurchasedItemOrder(
                        id = purchasedItem.order.id!!,
                        orderNumber = Order.generateOrderNumber(purchasedItem.order.id!!),
                        items =
                            purchasedItem.order.purchasedItems
                                .distinctBy { it.item.id }
                                .map {
                                    PurchasedItemDetail.PurchasedItemOrder.OrderedItem(
                                        id = it.id!!,
                                        name = it.item.name,
                                        itemType = it.item.itemType.value,
                                        price = it.price,
                                        marginRate = it.item.marginRate,
                                        quantity = it.quantity,
                                    )
                                },
                        orderedAt = Util.toJSTLocalDateTimeString(purchasedItem.order.createdAt!!),
                    ),
                checkout =
                    PurchasedItemDetail.PurchasedItemCheckout(
                        paymentType = checkout.type,
                        convenience = checkout.convenience,
                        confNo = checkout.confNo,
                        receiptNo = checkout.receiptNo,
                        paymentTerm =
                            if (checkout.paymentTerm != null) {
                                Util.toDateTimeString(checkout.paymentTerm!!)
                            } else {
                                null
                            },
                        receiptUrl = checkout.receiptUrl,
                        status = checkout.status,
                        total = checkout.total,
                        tipAmount = checkout.tip?.amount,
                        cvsFee = checkout.cvsFee,
                        deliveryFee = checkout.deliveryFee,
                    ),
                purchaserComment = purchasedItem.purchaserComment,
            )

        val result = if (input.applyMasking) MaskingProcessor.process(itemDetails) else itemDetails
        return Ok(result)
    }
}
