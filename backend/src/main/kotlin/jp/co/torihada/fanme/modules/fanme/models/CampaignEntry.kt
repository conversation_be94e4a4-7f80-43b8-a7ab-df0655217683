package jp.co.torihada.fanme.modules.fanme.models

import BaseModel
import com.fasterxml.jackson.annotation.JsonFormat
import jakarta.persistence.*
import jakarta.validation.constraints.NotNull
import java.time.Instant
import jp.co.torihada.fanme.modules.shop.Const

@PersistenceUnit(name = "fanme")
@Entity
@Table(name = "campaign_entries")
class CampaignEntry : BaseModel() {
    @ManyToOne @JoinColumn(name = "campaign_id", nullable = false) var campaign: Campaign? = null

    @NotNull @ManyToOne @JoinColumn(name = "user_id", nullable = false) var user: User? = null

    @Column(name = "entered_at", nullable = false)
    @JsonFormat(pattern = Const.DATE_RESPONSE_FORMAT, timezone = Const.DEFAULT_LOCAL_TIME_ZONE)
    var enteredAt: Instant = Instant.now()
}
