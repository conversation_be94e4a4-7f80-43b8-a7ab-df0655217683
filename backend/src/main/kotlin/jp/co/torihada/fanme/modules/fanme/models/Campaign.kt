package jp.co.torihada.fanme.modules.fanme.models

import BaseModel
import com.fasterxml.jackson.annotation.JsonFormat
import jakarta.persistence.*
import java.time.Instant
import jp.co.torihada.fanme.modules.shop.Const

@PersistenceUnit(name = "fanme")
@Entity
@Table(name = "campaigns")
class Campaign : BaseModel() {
    // NOTE:
    // https://www.figma.com/board/dj1gl7uBFa45EjVIeHCOc4/FANME%E9%96%8B%E7%99%BA%E7%92%B0%E5%A2%83?node-id=101-422&p=f&t=909tM75MddD4Uklo-0
    enum class EntryType(val value: String) {
        NONE("none"), // システムによるエントリーはなし（マニュアルエントリー）
        SIGNUP("signup"), // ユーザ新規登録でエントリー
        SHOP_CREATION("shop_creation"), // ショップ新規開設でエントリー
        SHOP_CREATION_ALL("shop_creation_all"); // ショップ新規開設でエントリー（ただし全員が対象）

        companion object {
            fun fromValue(value: String): EntryType {
                return values().find { it.value == value }
                    ?: throw IllegalArgumentException("Invalid EntryType value: $value")
            }
        }
    }

    enum class ActionType(val value: String) {
        NONE("none"),
        MARGIN_TYPE1("margin_type1"); // デジタル商品とデジタルファンレターのプラットフォーム手数料5%

        companion object {
            fun fromValue(value: String): ActionType {
                return values().find { it.value == value }
                    ?: throw IllegalArgumentException("Invalid ActionType value: $value")
            }
        }
    }

    @Column(name = "campaign_identity", unique = true, nullable = false)
    val campaignIdentity: String = ""

    @Column(nullable = false, length = 255) val title: String = ""

    @ManyToOne @JoinColumn(name = "user_id", nullable = true) var user: User? = null

    @Column(name = "entry_type", nullable = false, length = 50)
    @Enumerated(EnumType.STRING)
    val entryType: EntryType = EntryType.NONE

    @Column(name = "action_type", nullable = false, length = 50)
    @Enumerated(EnumType.STRING)
    val actionType: ActionType = ActionType.NONE

    @Column(name = "action_duration_days", nullable = false) val actionDurationDays: Int = 0

    @Column(name = "start_at", nullable = false)
    @JsonFormat(pattern = Const.DATE_RESPONSE_FORMAT, timezone = Const.DEFAULT_LOCAL_TIME_ZONE)
    var startAt: Instant = Instant.now()

    @Column(name = "end_at", nullable = false)
    @JsonFormat(pattern = Const.DATE_RESPONSE_FORMAT, timezone = Const.DEFAULT_LOCAL_TIME_ZONE)
    var endAt: Instant = Instant.now()
}
