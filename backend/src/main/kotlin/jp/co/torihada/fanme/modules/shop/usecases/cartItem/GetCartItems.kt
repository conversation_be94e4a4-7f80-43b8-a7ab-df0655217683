package jp.co.torihada.fanme.modules.shop.usecases.cartItem

import com.fasterxml.jackson.databind.PropertyNamingStrategies
import com.fasterxml.jackson.databind.annotation.JsonNaming
import com.github.michaelbull.result.Err
import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jp.co.torihada.fanme.annotations.Maskable
import jp.co.torihada.fanme.exception.FanmeException
import jp.co.torihada.fanme.exception.ResourceNotFoundException
import jp.co.torihada.fanme.lib.MaskingProcessor
import jp.co.torihada.fanme.modules.shop.Util
import jp.co.torihada.fanme.modules.shop.models.Cart
import jp.co.torihada.fanme.modules.shop.models.CartItem as CartItemModel
import jp.co.torihada.fanme.modules.shop.models.ItemType
import jp.co.torihada.fanme.modules.shop.models.Shop
import jp.co.torihada.fanme.modules.shop.services.DeliveryService

@ApplicationScoped
class GetCartItems {

    @Inject private lateinit var deliveryService: DeliveryService

    data class Input(val userUid: String, val creatorUid: String, val applyMasking: Boolean = true)

    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
    data class CartItem(
        val cartItemId: Long,
        val cartId: Long,
        val itemType: Int,
        val itemId: Long,
        val fileId: Long?,
        val quantity: Int,
        @Maskable val name: String,
        val thumbnailUri: String,
        val price: Int,
        val marginRate: Float,
        val currentPrice: Int,
        val discountRate: Float,
        val fileType: String?,
        val fileQuantities: List<FileQuantity>?,
        val forSale: Boolean,
        val soldOut: Boolean = false,
        val purchasableQuantity: Int?,
        val purchaserComment: String? = null,
    )

    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
    data class CartItemData(
        val cartItems: List<CartItem>,
        val deliveryFee: Int?,
        val isLocked: Boolean,
    )

    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
    data class FileQuantity(val fileType: String, val quantity: Int)

    fun execute(params: Input): Result<CartItemData, FanmeException> {
        val shop =
            Shop.findByCreatorUid(params.creatorUid)
                ?: return Err(ResourceNotFoundException("Shop"))
        val cart = Cart.findByUserUidAndShopId(params.userUid, shop.id!!)
        if (cart == null) {
            Cart.create(params.userUid, shop.id!!)
            return Ok(CartItemData(emptyList(), 0, false))
        }
        val cartItems = CartItemModel.findByCartId(cart.id!!)
        val deliveryFee = deliveryService.getDeliveryFee(cartItems.map { it.item })

        val purchasableQuantityMap: Map<Long, Int?> =
            if (cartItems.any { it.item.itemType == ItemType.CHEKI }) {
                cartItems.associate { it.id!! to Util.getPurchasableCount(it.item, params.userUid) }
            } else {
                mutableMapOf()
            }

        // カートアイテムをDTOに変換
        val cartItemDtos =
            cartItems.map { cartItem ->
                val item = cartItem.item
                val singleFile = cartItem.singleFile
                CartItem(
                    cartItemId = cartItem.id!!,
                    cartId = cart.id!!,
                    itemType = item.itemType.value,
                    itemId = item.id!!,
                    fileId = singleFile?.id,
                    quantity = cartItem.quantity,
                    name = singleFile?.name ?: item.name,
                    thumbnailUri = item.thumbnailUri,
                    price = singleFile?.price ?: item.price,
                    marginRate = item.marginRate,
                    currentPrice = Util.getCurrentPrice(cartItem),
                    discountRate = if (Util.onSale(item.onSale)) item.onSale!!.discountRate else 0f,
                    fileType = singleFile?.fileType,
                    fileQuantities = Util.getCartItemFileQuantities(cartItem),
                    forSale = Util.isItemForSale(cartItem),
                    soldOut = Util.isSoldOut(cartItem),
                    purchasableQuantity = purchasableQuantityMap[cartItem.id!!],
                    purchaserComment = cartItem.purchaserComment,
                )
            }

        val output =
            CartItemData(
                cartItems = cartItemDtos,
                isLocked = cart.isLocked,
                deliveryFee = deliveryFee,
            )

        return Ok(
            if (params.applyMasking)
                output.copy(cartItems = output.cartItems.map { MaskingProcessor.process(it) })
            else output
        )
    }
}
