'use client';

import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import toast from 'react-hot-toast';
import { sendGTMEvent } from '@next/third-parties/google';
import clsx from 'clsx';
import { useRouter } from 'next/navigation';
import Button from '@/components/atoms/button';
import SideButton from '@/components/atoms/button/side-button';
import NotificationWithNum from '@/components/atoms/notification/notification-with-num';
import Tab from '@/components/atoms/tab';
import Tabs from '@/components/atoms/tab/tabs';
import CustomToast from '@/components/atoms/toast/custom-toast';
import Instruction from '@/components/atoms/typography/instruction';
import SectionTitleWithIcon from '@/components/atoms/typography/section-title-with-icon';
import FlatItem from '@/components/containers/FlatItem';
import ReadMoreButton from '@/components/containers/ReadMoreButton';
import ShopItem from '@/components/containers/ShopItem';
import ShopPublicImage from '@/components/ShopImage';
import { useCurrentUser } from '@/store/useCurrentUser';
import { useModalsStore } from '@/store/useModals';
import BenefitItemList from '@/app/[identityId]/viewer/[itemId]/_components/benefit-item-list';
import { checkItemPasswordUnlockCache, createItemPasswordUnlockCache } from '@/app/actions/shopItem';
import { useGetCart } from '@/hooks/swr/useGetCart';
import { useCartItemActions } from '@/hooks/useCartItemActions';
import { usePeriodState } from '@/hooks/usePeriodState';
import { googleAnalyticsForEc } from '@/services/googleAnalyticsForEc';
import { cartItemsRequests } from '@/services/shopItemsRequests';
import { handleJumpAnimationWithArc } from '@/utils/animation';
import { getCartErrorMessage } from '@/utils/errorHandler';
import { getIsNowOnSale } from '@/utils/item';
import { CartItemRequest } from '@/types/api';
import { CartItem } from '@/types/cart';
import { Benefit, ITEM_TYPE, ItemFiles, OnSale, Period, PeriodState } from '@/types/shopItem';

type DetailItemsSectionProps = {
  props: {
    itemFiles: ItemFiles;
    itemId?: number;
    itemType: number;
    thumbnail: string;
    title: string;
    price: number;
    currentPrice?: number;
    discountRate?: number;
    discount?: {
      percentage: number;
      start?: Date | string;
      end?: Date | string;
    };
    thumbnailRatio: number;
    benefits?: Benefit[];
    singleSale: boolean;
    period?: Period;
    isPreview?: boolean;
    identityId: string;
    isPurchased?: boolean;
    isCheckout?: boolean;
    creatorId?: string;
    isSoldOut: boolean;
    hasPassword?: boolean;
    forceShowItemOption?: boolean;
  };
};

const DetailItemsSection = ({
  props: {
    itemFiles,
    itemId,
    itemType,
    thumbnail,
    title,
    price,
    currentPrice,
    discountRate,
    discount,
    thumbnailRatio,
    benefits,
    singleSale,
    period,
    isPreview,
    identityId,
    isPurchased,
    isCheckout,
    isSoldOut,
    hasPassword,
    forceShowItemOption,
  },
}: DetailItemsSectionProps) => {
  const router = useRouter();
  const { onModalClose, onModalOpen, setModalProps } = useModalsStore();
  const { removeItem } = useCartItemActions();
  const [value, setValue] = useState(0);
  const targetRef = useRef<HTMLDivElement>(null);
  const handleChange = (value: number) => {
    setValue(value);
  };
  const { periodState } = usePeriodState(period);
  const benefitItems = useMemo(
    () => benefits?.flatMap((benefit) => benefit.benefitFiles.filter((file) => file.title)) || [],
    [benefits],
  );

  const { currentUser } = useCurrentUser();
  const addCartSuccessToastOption = {
    id: 'add-cart-success',
  };
  const addCartFailedToastOption = {
    id: 'add-cart-failed',
  };

  const onSale = discount
    ? ({
        discountRate: discount.percentage,
        startAt: discount.start,
        endAt: discount.end,
      } as OnSale)
    : undefined;

  const isNowOnSale = getIsNowOnSale({
    startAt: discount?.start,
    endAt: discount?.end,
    discountRate: discount?.percentage,
  });

  // カートデータの取得
  const {
    data: cartData,
    error: cartError,
    refetch: refetchCart,
  } = useGetCart(identityId, currentUser?.accountIdentity, isPreview);

  // カートエラーのログ
  useEffect(() => {
    if (cartError) {
      console.error('Cart error:', cartError);
    }
  }, [cartError]);

  const isSetAlreadyInCart = useMemo(() => {
    if (!cartData) return false;
    return !!cartData?.data.items.find((item) => Number(item.itemId) === Number(itemId) && item.fileId === null);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [cartData, itemId]);

  const isSingleAlreadyInCart = useMemo(() => {
    if (!cartData) return false;
    return !!cartData?.data.items.find((item) => Number(item.itemId) === Number(itemId) && item.fileId !== null);
  }, [cartData, itemId]);

  const isSinglePurchased = useMemo(() => {
    if (!itemFiles) return false;
    return itemFiles.some((file) => file.isPurchased);
  }, [itemFiles]);

  const isButtonDisabled = useMemo(() => {
    // チェキの場合はカートに追加できる
    const isAllowedRepurchase = itemType === ITEM_TYPE.cheki;
    if (isAllowedRepurchase) {
      return isSoldOut;
    }
    return isSetAlreadyInCart || isPurchased || isCheckout || isSoldOut;
  }, [itemType, isSetAlreadyInCart, isPurchased, isCheckout, isSoldOut]);

  const buttonText = useMemo(() => {
    if (isSoldOut) return '売り切れ';
    // チェキの場合購入状態に関わらずカートに追加できる
    if (itemType === ITEM_TYPE.cheki) return 'カートに追加';
    if (isPurchased) return '購入済み';
    if (isCheckout) return 'お支払い待ち';
    if (isSetAlreadyInCart) return '追加済み';
    return 'カートに追加';
  }, [itemType, isPurchased, isCheckout, isSetAlreadyInCart, isSoldOut]);

  const handleOpenDialogForSingleAlreadyInCart = () => {
    onModalOpen();
    setModalProps({
      onClose: onModalClose,
      onConfirm: () => {
        onModalClose();
        handleReplaceToCart();
      },
      type: 'warning',
      children: (
        <div className="grid justify-items-center gap-4 px-4 pb-4 pt-10">
          <div className="text-center">本商品に含まれる単品商品がカートに入っています。本商品と入れ替えますか？</div>
        </div>
      ),
      confirmText: '入れ替える',
    });
  };

  const validateAndPrepareCartAdd = async (
    e: React.MouseEvent | undefined,
    type: 'set' | 'single',
    singleFileId?: string,
  ) => {
    const eventInfo = e
      ? ({
          currentTarget: e.currentTarget,
          clientX: e.clientX,
          clientY: e.clientY,
        } as React.MouseEvent<HTMLButtonElement>)
      : undefined;

    if (!checkLoginStatus()) return;
    if (!checkSetItemConditions(type)) return;

    const isPasswordValid = await checkPassword(type, singleFileId);
    if (!isPasswordValid) return;

    executeCartAdd(eventInfo, type, singleFileId);
  };

  const dialogTextBody = (
    <div className="grid items-center justify-center p-6 text-center">
      <p className="text-lg text-red-500">ただいま他の画面で決済手続き中です</p>
      <p> PayPayで決済を完了してください。</p>
      <p>５分以内に決済が行われない場合、</p>
      自動的に決済をキャンセルします。
    </div>
  );
  const handleOpenDialogWithMark = () => {
    onModalOpen();
    setModalProps({
      onClose: onModalClose,
      type: 'error',
      children: dialogTextBody,
    });
  };

  const executeCartAdd = async (e: React.MouseEvent | undefined, type: 'set' | 'single', singleFileId?: string) => {
    if (cartData?.data?.isLocked) {
      handleOpenDialogWithMark();
      return;
    }

    if (!itemId) {
      toast.custom((t) => CustomToast(t, 'error', 'カートへの商品追加に失敗しました'), addCartFailedToastOption);
      return;
    }
    if (type === 'single' && !singleFileId) {
      toast.custom((t) => CustomToast(t, 'error', 'カートへの商品追加に失敗しました'), addCartFailedToastOption);
      return;
    }

    // クリックの座標を保存
    const clickX = e?.clientX;
    const clickY = e?.clientY;
    const currentTarget = e?.currentTarget;

    const item: CartItemRequest = {
      itemId: Number(itemId),
      quantity: 1,
      singleFileId,
    };

    try {
      const res = await cartItemsRequests.addItemsToCart(identityId, {
        item,
      });
      if (res.statusText === 'success') {
        if (targetRef.current && currentTarget) {
          const syntheticEvent = {
            currentTarget,
            clientX: clickX,
            clientY: clickY,
          } as React.MouseEvent<HTMLButtonElement>;
          handleJumpAnimationWithArc(syntheticEvent, targetRef.current, {
            elementWidth: 15,
            elementHeight: 15,
            elementColor: '#de3a61',
            duration: 0.5,
            scale: 1,
            arcHeight: 50,
          });
        }
        refetchCart();
        sendGTMEvent({ event: 'fanme_cart_add', user_name: identityId, item_id: itemId });
        const addedCartItem = res.data.data.cart_item;
        const cartItem =
          type === 'set'
            ? ({
                itemId: addedCartItem.item.id,
                price: addedCartItem.item.price,
                quantity: addedCartItem.quantity,
                title: addedCartItem.item.name,
              } as CartItem)
            : ({
                itemId: addedCartItem.single_file.id,
                price: addedCartItem.single_file.price,
                quantity: addedCartItem.quantity,
                title: addedCartItem.single_file.name,
              } as CartItem);
        googleAnalyticsForEc.addToCart(cartItem);
        toast.custom((t) => CustomToast(t, 'success', 'カートに商品が追加されました'), addCartSuccessToastOption);
      } else {
        toast.custom((t) => CustomToast(t, 'error', res.data.message), addCartFailedToastOption);
      }
    } catch (error: any) {
      console.error('カートへの商品追加に失敗しました', error);
      const errorCode = error.error?.code ?? 9999;
      const errorMessage = getCartErrorMessage(errorCode);
      toast.custom((t) => CustomToast(t, 'error', errorMessage), addCartFailedToastOption);
    }
  };

  const handleReplaceToCart = async () => {
    const shouldDeleteItems = cartData?.data.items.filter(
      (item) => Number(item.itemId) === Number(itemId) && item.fileId !== null,
    );
    if (shouldDeleteItems) {
      for (const item of shouldDeleteItems) {
        await removeItem(item.cartItemId);
      }
    }
    await executeCartAdd(undefined, 'set');
  };

  const handleOpenDialogForAlreadySinglePurchased = () => {
    onModalOpen();
    setModalProps({
      onClose: onModalClose,
      onConfirm: () => {
        onModalClose();
        executeCartAdd(undefined, 'set');
      },
      type: 'warning',
      children: (
        <div className="grid justify-items-center gap-4 px-4 pb-4 pt-10">
          <div className="text-center">本商品に含まれる単品商品をご購入済みです。本商品をカートに入れますか？</div>
        </div>
      ),
      confirmText: 'カートに入れる',
    });
  };

  const singleSaleFiles = useMemo(() => {
    if (!itemFiles) return [];
    // isPreview なら全ての商品を返す
    if (isPreview) return itemFiles.filter((file) => !!file.price);

    // isPreview 以外の場合
    itemFiles.forEach((file) => {
      file.isInCart = isSetAlreadyInCart
        ? true
        : cartData?.data.items.some(
            (cartItem) => Number(cartItem.itemId) === Number(itemId) && Number(cartItem.fileId) === Number(file.id),
          ) || false;
    });

    // 価格がある全ての商品を返す
    return itemFiles.filter((file) => !!file.price);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [itemFiles, cartData?.data.items, itemId, isPreview]);

  const gotoLoginPage = () => {
    const returnUrl = window.location.href;
    const loginUrl = `${process.env.NEXT_PUBLIC_OLD_FANME_API_HOST}/creators/auth/fanme?return_url=${encodeURIComponent(
      returnUrl,
    )}`;
    router.push(loginUrl);
  };

  const [isPasswordLoading, setIsPasswordLoading] = useState(false);

  const PasswordInputModal = () => {
    const [inputPassword, setInputPassword] = useState('');

    return (
      <div className="grid justify-items-center gap-4 px-4 pb-4 pt-10">
        <div className="text-center">
          購入するには
          <br />
          パスワードを入力してください
        </div>
        <div className="relative w-full">
          <input
            type="password"
            value={inputPassword}
            onChange={(e) => setInputPassword(e.target.value)}
            className="w-full rounded-lg border border-gray-300 px-4 py-2 pr-10 text-left placeholder:text-left"
            placeholder="パスワードを入力"
            autoFocus
            disabled={isPasswordLoading}
          />
        </div>
      </div>
    );
  };

  const handlePasswordSubmit = useCallback(
    async (password: string, onSuccess?: () => void) => {
      setIsPasswordLoading(true);
      try {
        const res = await createItemPasswordUnlockCache(identityId, itemId || 0, password);
        if (res.statusText === 'success') {
          onModalClose();
          onSuccess?.();
        } else {
          toast.custom((t) => CustomToast(t, 'error', 'パスワードが正しくありません'), addCartFailedToastOption);
        }
      } catch (error) {
        console.error('パスワードが正しくありません', error);
        toast.custom((t) => CustomToast(t, 'error', 'パスワードが正しくありません'), addCartFailedToastOption);
      } finally {
        setIsPasswordLoading(false);
      }
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [identityId, itemId, onModalClose],
  );

  const handleOpenPasswordModal = useCallback(
    (onSuccess?: () => void) => {
      onModalOpen();

      setModalProps({
        onClose: () => {
          onModalClose();
        },
        onConfirm: () => {
          const modalContent = document.querySelector('input[type="password"]') as HTMLInputElement;
          if (modalContent) {
            handlePasswordSubmit(modalContent.value, onSuccess);
          }
        },
        children: <PasswordInputModal />,
        confirmText: 'カートに追加',
        buttonType: isPasswordLoading ? 'disabled' : 'primary',
      });
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [handlePasswordSubmit, isPasswordLoading, onModalOpen, onModalClose],
  );

  // ログインチェック
  const checkLoginStatus = useCallback(
    () => {
      if (!currentUser) {
        toast.custom((t) => CustomToast(t, 'error', '商品を購入するにはログインが必要です'), addCartFailedToastOption);
        setTimeout(() => gotoLoginPage(), 1500);
        return false;
      }
      return true;
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [currentUser],
  );

  // セット商品の確認モーダル表示
  const checkSetItemConditions = useCallback(
    (type: 'set' | 'single'): boolean => {
      // チェキの場合はセット商品同様、チェックをスキップ
      if (itemType === ITEM_TYPE.cheki) return true;
      if (type !== 'set') return true;

      if (isSingleAlreadyInCart) {
        handleOpenDialogForSingleAlreadyInCart();
        return false;
      }
      if (isSinglePurchased) {
        handleOpenDialogForAlreadySinglePurchased();
        return false;
      }
      return true;
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [isSingleAlreadyInCart, isSinglePurchased],
  );

  // パスワードチェックとモーダル表示
  const checkPassword = useCallback(
    async (type: 'set' | 'single', singleFileId?: string): Promise<boolean> => {
      if (!hasPassword) return true;

      try {
        const res = await checkItemPasswordUnlockCache(identityId, itemId || 0);

        if (!res.data) {
          handleOpenPasswordModal(() => {
            executeCartAdd(undefined, type, singleFileId);
          });
          return false;
        }
        return true;
      } catch (error) {
        if (error instanceof Error) {
          console.error('Error details:', error.message);
        }
        toast.custom((t) => CustomToast(t, 'error', 'パスワードが違います'), addCartFailedToastOption);
        return false;
      }
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [hasPassword, identityId, itemId],
  );

  return (
    <>
      {!isPreview && (
        <div className="fixed bottom-24 right-[calc((100vw-480px)/2)] z-50 max-PC:right-0" ref={targetRef}>
          {!!cartData?.data.totalQuantity && (
            <NotificationWithNum num={cartData.data.totalQuantity} className="absolute right-0 top-0 z-10" />
          )}
          <SideButton
            sideButtonType="dark"
            buttonDirection="left"
            buttonSize="lg"
            onClick={() => router.push(`/@${identityId}/cart`)}
          >
            <ShopPublicImage src="/images/icons/Cart.svg" width={28} height={28} alt="cart" />
          </SideButton>
        </div>
      )}
      <div className="mb-11 mt-6 px-4">
        <SectionTitleWithIcon title="商品" icon="/images/icons/Shop.svg" />
        <div className="mt-3 grid place-items-center">
          {singleSale && (
            <Tabs value={value} onChange={handleChange}>
              <Tab label="セット" />
              <Tab label="単品" />
            </Tabs>
          )}
          {value === 0 && (
            // セット
            <div className={clsx('w-full', { 'mt-6': singleSale })}>
              <FlatItem
                id={itemId ? itemId : 0}
                itemType={itemType}
                imageUrl={thumbnail!}
                title={title!}
                isSet={true}
                price={price}
                currentPrice={currentPrice}
                discountRate={discountRate}
                isNowOnSale={isNowOnSale}
                thumbnailRatio={thumbnailRatio!}
                files={itemFiles?.map((file) => ({
                  ...file,
                  type: file.type || 'image',
                }))}
                showRemoveButton={false}
                isStarted={periodState === PeriodState.BeforeStart}
                isEnded={periodState === PeriodState.Ended}
                forceShowItemOption={forceShowItemOption}
                isPreview={isPreview}
              />
              {!!benefitItems?.length && (
                <div className="bg-yellow-50 px-3 pb-2 pt-3">
                  <div className="mb-2 flex items-center justify-between">
                    <SectionTitleWithIcon
                      title="セット購入特典"
                      icon="/images/icons/Present.svg"
                      className="text-orange-300"
                    />
                    {itemType !== ITEM_TYPE.cheki && (
                      <Instruction className="text-red-500">* セット購入時のみの限定特典です</Instruction>
                    )}
                  </div>
                  {benefits?.map((benefit, index) =>
                    benefit.description ? (
                      <div key={index} className="mb-2 whitespace-pre-wrap rounded-lg bg-white px-3 py-2 text-gray-800">
                        <ReadMoreButton description={benefit.description} />
                      </div>
                    ) : null,
                  )}
                  {benefits && !!benefitItems?.length && <BenefitItemList items={benefits} readOnly={true} />}
                </div>
              )}
              <div className="mt-6 flex flex-col items-center justify-center gap-y-4">
                {/* 動線不明、一旦コメントアウト <Button buttonType="secondary">今すぐ買う</Button> */}
                {periodState !== PeriodState.BeforeStart && periodState !== PeriodState.Ended && (
                  <Button
                    buttonType={isButtonDisabled ? 'disabled' : 'primary'}
                    onClick={(e) => {
                      if (isButtonDisabled || isPreview) return;
                      validateAndPrepareCartAdd(e, 'set');
                    }}
                    className={isPreview ? 'pointer-events-none cursor-not-allowed' : ''}
                  >
                    <div className="relative w-full">
                      {hasPassword && (
                        <div className="absolute left-4 top-1/2 -translate-y-1/2">
                          <ShopPublicImage
                            src={isButtonDisabled ? '/images/icons/LockWhite.svg' : '/images/icons/Lock.svg'}
                            width={20}
                            height={20}
                            alt="lock"
                          />
                        </div>
                      )}
                      <div className="text-center">{buttonText}</div>
                    </div>
                  </Button>
                )}
              </div>
            </div>
          )}
          {value === 1 && (
            // 単品
            <div className="mt-6 grid w-full grid-cols-1 gap-4">
              {singleSaleFiles.map((file) => (
                <ShopItem
                  key={file.id}
                  itemId={itemId || 0}
                  thumbnail={file.thumbnail!}
                  title={file.title!}
                  fileType={file.type!}
                  price={file.price || 0}
                  minPrice={file.price || 0}
                  currentPrice={file.currentPrice || 0}
                  extension={file.extension}
                  size={file.size}
                  duration={file.duration}
                  isStarted={periodState === PeriodState.BeforeStart}
                  isPurchased={isPurchased || file.isPurchased} // セット商品購入済みも単品購入済みにする
                  isCheckout={file.isCheckout}
                  isAdded={file.isInCart}
                  onAddToCart={(e) => {
                    if (isPurchased || file.isInCart) return;
                    validateAndPrepareCartAdd(e, 'single', file.id);
                  }}
                  onSale={onSale}
                  identityId={identityId || ''}
                  hasBenefit={false}
                  isPreview={isPreview}
                  isExpired={periodState === PeriodState.Ended}
                />
              ))}
            </div>
          )}
        </div>
      </div>
    </>
  );
};

export default DetailItemsSection;
