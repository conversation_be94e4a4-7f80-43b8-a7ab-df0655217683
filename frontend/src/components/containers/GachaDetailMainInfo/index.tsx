'use client';

import clsx from 'clsx';
import StateBadge from '@/components/atoms/badges/state-badge';
import Instruction from '@/components/atoms/typography/instruction';
import DetailPeriodSection from '@/components/containers/DetailMainInfo/detail-period-section';
import ShopPublicImage from '@/components/ShopImage';
import { Period } from '@/types/shopItem';

export type GachaDetailMainInfoProps = {
  itemId: string;
  limited?: number;
  isPreview?: boolean;
  totalPulledCount: number;
  period?: Period;
};

const GachaDetailMainInfo = ({ props }: { props: GachaDetailMainInfoProps }) => {
  const { limited, period, isPreview = false, totalPulledCount } = props;

  return (
    <div>
      <div className="mt-4 flex items-center justify-start">
        <StateBadge type="half-round-filled" color="white">
          <ShopPublicImage src="/images/gacha/icons/Gacha.svg" alt="left" width={16} height={16} className="mr-1.5" />
          抽選回数
        </StateBadge>
        <div className="ml-3 align-middle">
          <span className={clsx('mr-1 text-bold-18', !!totalPulledCount ? 'text-gray-800' : 'text-gray-400')}>
            {totalPulledCount ?? 0}
          </span>
          <span className={clsx('text-medium-15', !!totalPulledCount ? 'text-gray-800' : 'text-gray-400')}>回</span>
        </div>
      </div>
      {!!limited && (
        <div className="mt-4 flex items-center justify-start">
          <StateBadge type="half-round-filled" color="white">
            <ShopPublicImage src="/images/icons/Stock.svg" alt="left" width={16} height={16} className="mr-1.5" />
            限定販売
          </StateBadge>
          <div className="ml-3 flex items-center align-middle">
            <ShopPublicImage src="/images/gacha/icons/RarityS.svg" alt="left" width={30} height={20} className="mr-3" />
            <span className="text-medium-13 text-gray-800">合計{limited}点排出で販売終了</span>
          </div>
        </div>
      )}

      {(period?.end || period?.start) && <DetailPeriodSection period={period} isPreview={isPreview} />}
      <div className="mt-4 pl-4">
        <Instruction>
          *出品者が非公開・削除した場合は購入ができなくなります。
          <br />
          *各レア度にあらかじめ確率が設定されています。
        </Instruction>
      </div>
    </div>
  );
};

export default GachaDetailMainInfo;
