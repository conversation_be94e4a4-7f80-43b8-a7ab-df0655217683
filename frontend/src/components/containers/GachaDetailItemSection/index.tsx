'use client';

import Instruction from '@/components/atoms/typography/instruction';
import SectionTitleWithIcon from '@/components/atoms/typography/section-title-with-icon';
import ShopPublicImage from '@/components/ShopImage';
import AwardProbabilityDisplay from './award-probability-display';
import BenefitItemList from '@/app/[identityId]/viewer/[itemId]/_components/benefit-item-list';
import { useGroupGachaItems } from '@/hooks/useGroupGachaItems';
import { bytesToMB } from '@/utils/base';
import { handleAwardProbability } from '@/utils/gacha-helpers';
import { downloadSingleFile } from '@/utils/file';
import type { GachaBenefit, GachaItemFile, GachaBenefitFile, AwardProbability } from '@/types/gacha';
import type { Period } from '@/types/shopItem';

export type GachaDetailItemSectionProps = {
  itemFiles: GachaItemFile[];
  itemId?: number;
  thumbnail: string;
  title: string;
  price: number;
  currentPrice?: number;
  discountRate?: number;
  discount?: {
    percentage: number;
    start?: Date | string;
    end?: Date | string;
  };
  thumbnailRatio: number;
  benefits?: GachaBenefit[];
  period?: Period;
  isPreview?: boolean;
  identityId: string;
  isPurchased?: boolean;
  isCheckout?: boolean;
  creatorId?: string;
  isSoldOut: boolean;
  hasPassword?: boolean;
  forceShowItemOption?: boolean;
  totalCapacity: number;
  awardProbabilities: AwardProbability[];
  isDuplicated?: boolean;
};

const GachaDetailItemsSection = ({
  props: { itemFiles, benefits, totalCapacity, awardProbabilities, isDuplicated, isPreview, itemId },
}: {
  props: GachaDetailItemSectionProps;
}) => {
  const groupedItemsByAwardType = useGroupGachaItems(itemFiles);

  // Handle download for individual gacha benefit files
  const handleBenefitFileDownload = async (benefitFile: GachaBenefitFile) => {
    if (itemId) {
      await downloadSingleFile(itemId.toString(), benefitFile);
    }
  };
  return (
    <>
      <div className="mb-4 mt-6 px-4">
        <div className="mb-4 flex items-center justify-between">
          <SectionTitleWithIcon title="ガチャ内容" icon="/images/gacha/icons/Gacha.svg" />
          <span className="text-medium-12 text-gray-550">合計容量: {bytesToMB(totalCapacity).toFixed(2)}MB</span>
        </div>
        {/* 内容 */}
        <div>
          {groupedItemsByAwardType.map((awardProb, idx) => {
            const items = awardProb;
            if (items.length === 0) return null;
            const { awardType, probability } = handleAwardProbability(items, awardProbabilities);

            return (
              <div key={idx} className="mb-5">
                <div className="mb-2.5 flex items-center justify-start gap-1.5">
                  <AwardProbabilityDisplay
                    awardType={awardType}
                    probability={probability}
                    isShowProbability={isDuplicated}
                  />
                </div>
                <div className="mb-2.5 flex flex-wrap items-center gap-3">
                  {items.map((item: GachaItemFile) => (
                    <div key={item.id} className="relative">
                      <img
                        src={
                          item.isSecret
                            ? '/shop/images/gacha/icons/Secret.svg'
                            : item.type === 'audio'
                              ? '/shop/images/voice.png'
                              : item.maskedThumbnailUri
                        }
                        alt={item.title}
                        width={52}
                        height={52}
                      />
                      {item.receivedFileCount !== undefined && item.receivedFileCount > 0 && !isPreview && (
                        <ShopPublicImage
                          src={'/images/gacha/icons/Finish.svg'}
                          alt="finish"
                          width={64}
                          height={64}
                          className="absolute inset-0 m-auto"
                        />
                      )}
                    </div>
                  ))}
                </div>
              </div>
            );
          })}
          <div className="mt-4">
            <Instruction>
              *各賞に記載している出現率は各賞全体の出現率です。
              <br /> 各賞に含まれる景品の出現確率は均等確率となります。
              <br /> 例） S賞が10％、景品2点の場合、1点の確率は5%
              <br /> *確率は1回ごとに、各賞の当選確率にもとづいて抽選を行います。
            </Instruction>
          </div>
        </div>
        <div className="mt-3 grid place-items-center">
          <div className="w-full">
            {!!benefits?.length && (
              <div className="bg-yellow-50 px-3 pb-2 pt-3">
                <div className="mb-2 flex items-center justify-between">
                  <SectionTitleWithIcon title="特典" icon="/images/icons/Present.svg" className="text-orange-300" />
                </div>
                <BenefitItemList items={benefits} readOnly={false} onFileDownload={handleBenefitFileDownload} />
              </div>
            )}
          </div>
        </div>
      </div>
    </>
  );
};

export default GachaDetailItemsSection;
