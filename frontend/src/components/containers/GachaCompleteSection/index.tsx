'use client';
import clsx from 'clsx';
import Link from 'next/link';
import { useParams } from 'next/navigation';
import Instruction from '@/components/atoms/typography/instruction';
import GachaCompleteBadge from '@/components/containers/GachaCompleteBadge';
import ShopPublicImage from '@/components/ShopImage';
import { useGetCompleteBadge } from '@/lib/client-api/digital-gacha-endpoint/digital-gacha-endpoint';
import { roboto } from '@/app/fonts';
import { getUserIdentityId } from '@/utils/base';

export type GachaCompleteSectionProps = {
  isPreview: boolean;
  totalGachaCount: number;
  collectedUniqueItemsCount: number;
  isCompleted: boolean;
  thumbnail: string;
  itemId: number;
};

const GachaCompleteSection = ({
  props: { isPreview, totalGachaCount, collectedUniqueItemsCount, isCompleted, thumbnail, itemId },
}: {
  props: GachaCompleteSectionProps;
}) => {
  const params = useParams();
  const identityId = getUserIdentityId(params.identityId as string);

  const { data: completeBadgeData } = useGetCompleteBadge(itemId, {
    swr: {
      enabled: isCompleted && !isPreview,
    },
  });

  const shouldShowBadge = isPreview || (isCompleted && completeBadgeData?.data?.badge.rank);
  const completeRank = isPreview ? 1 : completeBadgeData?.data?.badge.rank || 1;

  return (
    <div className="flex flex-col items-center justify-center gap-5 rounded-t-2xl bg-white pb-4 pt-6 drop-shadow-s">
      <div className="flex justify-center">
        <ShopPublicImage
          src="/images/gacha/icons/gacha_complete_description.svg"
          alt="Gacha Complete Description"
          width={296}
          height={72}
        />
      </div>

      <div className="flex w-86 flex-col gap-3">
        {shouldShowBadge && <GachaCompleteBadge creatorAvatar={thumbnail} completeRank={completeRank} />}

        {isCompleted ? (
          <div className="flex items-center justify-center gap-3">
            <div className={clsx('flex h-8 items-center justify-start gap-8 rounded-lg bg-gray-700 px-2')}>
              <ShopPublicImage src="/images/gacha/icons/gacha_white.svg" alt="gacha icon" width={20} height={20} />
              <div className="flex items-center">
                <span className={clsx('gradient-complete-text text-bold-20', roboto.className)}>COMPLETE!!</span>
              </div>
            </div>
            <Link
              href={`/${identityId}/item/${itemId}/complete-ranking`}
              className="flex h-8 items-center justify-center gap-1 rounded-2xl border border-secondary bg-white px-3 text-bold-12"
            >
              <ShopPublicImage src="/images/icons/Ranking.svg" alt="Ranking" width={16} height={16} />
              ランキング
            </Link>
          </div>
        ) : (
          <>
            <div className="flex justify-center">
              <Link
                href={`/${identityId}/item/${itemId}/complete-ranking`}
                className="flex h-8 items-center justify-center gap-1 rounded-2xl border border-secondary bg-white px-3 text-bold-12"
              >
                <ShopPublicImage src="/images/icons/Ranking.svg" alt="Ranking" width={16} height={16} />
                ランキング
              </Link>
            </div>
            <div className="flex justify-end">
              <div className="flex items-center gap-2">
                <ShopPublicImage src="/images/gacha/icons/gacha_bk.svg" alt="Gacha" width={20} height={20} />
                <div
                  className={clsx(collectedUniqueItemsCount > 0 ? 'text-secondary' : 'text-gray-400', roboto.className)}
                >
                  <span className="text-bold-20">{isPreview ? totalGachaCount : collectedUniqueItemsCount}</span>
                  <span className="text-regular-16">/{totalGachaCount}</span>
                </div>
              </div>
            </div>
          </>
        )}

        {isPreview && <Instruction>*図鑑はコンプリートしたときのイメージです</Instruction>}
      </div>
    </div>
  );
};

export default GachaCompleteSection;
