'use client';

import React, { useMemo, memo } from 'react';
import { Control } from 'react-hook-form';
import Button from '@/components/atoms/button';
import SelectInput from '@/components/atoms/inputs/select-input';
import TextAreaInput from '@/components/atoms/inputs/text-area-input';
import UploadedFile from '@/components/containers/UploadedFile';
import UploadFile from '@/components/containers/UploadFile';
import ShopPublicImage from '@/components/ShopImage';
import { ShopLimitation } from '@/lib/server-api/shop-api.schemas';
import { useUploadProgress } from '@/hooks/useUploadProgress';
import { type ConditionType, CONDITION_TYPE, type GachaBenefitFile } from '@/types/gacha';
import { ItemFiles } from '@/types/shopItem';

interface BenefitItemProps {
  benefitId: number;
  index: number;
  condition: ConditionType;
  description: string;
  benefitFiles: GachaBenefitFile[] | null;
  availableConditions: { value: ConditionType; label: string }[];
  textLength: number;
  canDelete: boolean;
  isLastItem: boolean;
  control: Control<any>;
  shopLimitation: ShopLimitation;
  onConditionChange: (benefitId: number, condition: ConditionType) => void;
  onDescriptionChange: (benefitId: number, description: string) => void;
  onFileUpload: (benefitId: number, files: ItemFiles) => void;
  onFileDelete: (benefitId: number) => void;
  onDelete: (benefitId: number) => void;

  isEdit?: boolean;
}

const BenefitItem: React.FC<BenefitItemProps> = ({
  benefitId,
  index,
  condition,
  description,
  benefitFiles,
  availableConditions,
  textLength,
  canDelete,
  isLastItem,
  control,
  shopLimitation,
  onConditionChange,
  onDescriptionChange,
  onFileUpload,
  onFileDelete,
  onDelete,
  isEdit = false,
}) => {
  const { uploadProgress, handleProgress } = useUploadProgress();
  const benefitItemSrc = useMemo(() => {
    if (benefitFiles?.[0]?.type === 'image' || benefitFiles?.[0]?.type === 'video') {
      return benefitFiles[0]?.thumbnail || '';
    } else if (benefitFiles?.[0]?.type === 'audio') {
      return '/shop/images/voice.png';
    }
    return '';
  }, [benefitFiles]);

  const handleEdit = () => {
    onFileDelete(benefitId);
  };

  return (
    <div className={`${isLastItem ? '' : 'section-double-border'} py-4`}>
      <div>
        <div className="mb-5 flex items-center justify-start gap-2">
          <div className="w-18 rounded-lg bg-secondary text-center text-medium-12 leading-8 text-white">
            特典条件 {index + 1}
          </div>
          <SelectInput
            control={control}
            inputName={`benefits.${benefitId}.condition`}
            options={availableConditions}
            required
            className="!mb-0 flex-1"
            onChange={(value) => onConditionChange(benefitId, value as ConditionType)}
            defaultValue={isEdit ? condition.toString() : undefined}
            disabled={isEdit}
          />
        </div>

        {/* ファイルアップロード */}
        <div className="mb-4 flex items-center justify-center">
          {benefitFiles && benefitFiles.length > 0 ? (
            <UploadedFile
              id={benefitId.toString()}
              src={benefitItemSrc}
              type={benefitFiles[0]?.type || 'image'}
              setThumbImage={() => {}}
              handleDelete={!isEdit ? handleEdit : undefined}
              progress={uploadProgress[benefitId.toString()] || 0}
              isLoading={benefitFiles?.[0]?.isLoading || false}
              uploadType="benefit"
              showTitle={false}
              title={benefitFiles[0]?.title || ''}
              conditionType={condition || CONDITION_TYPE.TIMES_10}
            />
          ) : (
            <UploadFile
              setFiles={(files) => onFileUpload(benefitId, files)}
              shopLimitation={shopLimitation}
              isPublic={false}
              onProgress={handleProgress}
            />
          )}
        </div>

        {/* 特典内容 */}
        <TextAreaInput
          control={control}
          inputName={`benefits.${benefitId}.description`}
          labelTitle="特典内容"
          placeholder="特典内容を入力してください"
          maxLength={30}
          textLength={textLength}
          defaultValue={description}
          onBlur={(description) => onDescriptionChange(benefitId, description || '')}
          disabled={isEdit}
        />

        {/* 削除ボタン */}
        {canDelete && !isEdit && (
          <div className="flex justify-center">
            <Button buttonType="light-shadow" buttonSize="md" onClick={() => onDelete(benefitId)}>
              <ShopPublicImage
                src="/images/icons/Delete-error.svg"
                className="mr-1"
                width={12}
                height={12}
                alt="delete"
              />
              <span className="text-medium-12 text-error">特典を削除</span>
            </Button>
          </div>
        )}
      </div>
    </div>
  );
};

export default memo(BenefitItem);
