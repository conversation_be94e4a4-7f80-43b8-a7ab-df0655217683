'use client';

import toast from 'react-hot-toast';
import { sendGTMEvent } from '@next/third-parties/google';
import { useRouter } from 'next/navigation';
import CustomToast from '@/components/atoms/toast/custom-toast';
import ChekiItem from '@/components/containers/ChekiItem';
import ShopItem from '@/components/containers/ShopItem';
import { ITEM_TYPE } from '@/types/item';
import { ShopItemType } from '@/types/shopItem';

type ItemListProps = {
  items: ShopItemType[];
  identityId: string;
};

const ShopItemList = ({ items, identityId }: ItemListProps) => {
  const router = useRouter();
  const handleShopItemEvent = (itemId: number) => {
    sendGTMEvent({
      event: 'fanme_item_detail_click',
      item_id: itemId,
    });
    const item = items?.find((item) => item.id === itemId);
    if (!item) {
      toast.custom((t) => CustomToast(t, 'error', '商品が見つかりませんでした'));
      return;
    }

    // デジタルバンドルは購入済みの場合、viewerに遷移、他は再度購入できるよう商品詳細に遷移
    const itemPath = item.isPurchased && item.itemType === ITEM_TYPE.DIGITAL_BUNDLE.value ? 'viewer' : 'item';

    router.push(`/@${identityId}/${itemPath}/${itemId}`);
  };
  return (
    <div className="flex w-full flex-col items-center justify-center gap-6">
      {items?.map((item) => {
        const commonProps = {
          itemId: item.id,
          thumbnail: item.thumbnail,
          isNew: item.isNew,
          title: item.title,
          price: item.price,
          currentPrice: item.currentPrice,
          minPrice: item.minPrice,
          isAdded: item.isAdded,
          isCheckout: item.isCheckout,
          hasPassword: item.hasPassword,
          onSale: item.onSale,
          forSale: item.forSale,
          hasBenefit: item.hasBenefit,
          remainingAmount: item.stockCount,
          onShopItemEvent: handleShopItemEvent,
        };

        return item.itemType === ITEM_TYPE.CHEKI.value ? (
          <ChekiItem key={item.id} {...commonProps} />
        ) : (
          <ShopItem
            key={item.id}
            {...commonProps}
            identityId={identityId}
            itemType={item.itemType}
            setType={item.fileType}
            isSet={true}
            isPurchased={item.isPurchased}
            isSingleSales={item.isSingleSales}
            collectedUniqueItemsCount={item.collectedUniqueItemsCount}
            totalItemFileCount={item.fileQuantities?.reduce((acc, file) => acc + file.quantity, 0)}
          />
        );
      })}
    </div>
  );
};

export default ShopItemList;
