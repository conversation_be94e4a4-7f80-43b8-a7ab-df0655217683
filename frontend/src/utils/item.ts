import moment from 'moment';
import { ShopLimitation } from '@/lib/server-api/shop-api.schemas';
import { bytesToMB } from './base';
import { isFuture, isNowBetween, isBefore } from './time';
import { SHOP_LIMITATION } from '@/consts/limitation';
import {
  ITEMS_FILES_SIZE,
  MAX_AUDIO_ITEM_COUNT,
  MAX_IMAGE_ITEM_COUNT,
  MAX_ITEM_COUNT,
  MAX_VIDEO_ITEM_COUNT,
} from '@/consts/sizes';
import type { ForSale, OnSale } from '@/types/shopItem';
import { ItemFiles } from '@/types/shopItem';

export const convertToSetTypeMap = (setType: string) => {
  return {
    video: setType?.charAt(2) === '1',
    audio: setType?.charAt(1) === '1',
    image: setType?.charAt(0) === '1',
  };
};

export const validateUploadedFiles = (
  currentFiles: FileList,
  tempItemFiles: ItemFiles,
  shopLimitation: ShopLimitation,
): string | null => {
  const { IMAGE, AUDIO, VIDEO } = ITEMS_FILES_SIZE;

  const { fileQuantity, fileCapacity } = shopLimitation;

  const isFileQuantityDefault = fileQuantity == SHOP_LIMITATION.FILE_QUANTITY.DEFAULT_LIMITATION;
  const isFileCapacityDefault = fileCapacity == SHOP_LIMITATION.FILE_CAPACITY.DEFAULT_LIMITATION;

  // アップロードされたファイルのリストを分類する
  const currentFilesArray = Array.from(currentFiles);
  const imageFiles = currentFilesArray.filter((file) => file.type.includes('image'));
  const audioFiles = currentFilesArray.filter((file) => file.type.includes('audio'));
  const videoFiles = currentFilesArray.filter((file) => file.type.includes('video'));

  // 現在のファイル数を計算
  const existingImageFiles = tempItemFiles.filter((file) => file.type === 'image');
  const existingAudioFiles = tempItemFiles.filter((file) => file.type === 'audio');
  const existingVideoFiles = tempItemFiles.filter((file) => file.type === 'video');

  // ファイル数のチェック
  if (isFileQuantityDefault) {
    // 種類別のファイル数をチェック
    if (imageFiles.length + existingImageFiles.length > MAX_IMAGE_ITEM_COUNT) {
      return `アップロード可能な画像数は${MAX_IMAGE_ITEM_COUNT}個までです`;
    }

    if (audioFiles.length + existingAudioFiles.length > MAX_AUDIO_ITEM_COUNT) {
      return `アップロード可能な音声数は${MAX_AUDIO_ITEM_COUNT}個までです`;
    }

    if (videoFiles.length + existingVideoFiles.length > MAX_VIDEO_ITEM_COUNT) {
      return `アップロード可能な動画数は${MAX_VIDEO_ITEM_COUNT}個までです`;
    }

    // トータルファイル数をチェック
    if (currentFiles.length + tempItemFiles.length > MAX_ITEM_COUNT) {
      return `アップロード可能な数は${MAX_ITEM_COUNT}個までです`;
    }
  }

  // ファイルサイズのチェック
  if (isFileCapacityDefault) {
    // 1ファイルのサイズをチェック
    const sizeErrors = currentFilesArray
      .map((file) => {
        if (file.size === 0) return `ファイルサイズが0のためアップロードできません`;
        if (
          (file.type.includes('image') && file.size > IMAGE.MAX_SINGLE_IMAGE_SIZE) ||
          (file.type.includes('audio') && file.size > AUDIO.MAX_SINGLE_AUDIO_SIZE) ||
          (file.type.includes('video') && file.size > VIDEO.MAX_SINGLE_VIDEO_SIZE)
        ) {
          const currentFileTypeText = file.type.includes('image')
            ? '画像'
            : file.type.includes('audio')
              ? '音声'
              : '動画';
          const currentFileTypeSize = file.type.includes('image')
            ? IMAGE.MAX_SINGLE_IMAGE_SIZE
            : file.type.includes('audio')
              ? AUDIO.MAX_SINGLE_AUDIO_SIZE
              : VIDEO.MAX_SINGLE_VIDEO_SIZE;
          return `${currentFileTypeText}は${bytesToMB(currentFileTypeSize)}MBまでアップロードできます`;
        }
        return null;
      })
      .filter(Boolean);

    if (sizeErrors.length > 0 && sizeErrors[0]) return sizeErrors[0];

    // 各種ファイルの合計サイズをチェック
    // 画像合計サイズチェック
    const newImageFilesSize = imageFiles.reduce((acc, file) => acc + file.size, 0);
    const existingImageFilesSize = existingImageFiles.reduce((acc, file) => acc + (file.size || 0), 0);
    if (newImageFilesSize + existingImageFilesSize > IMAGE.MAX_ALL_IMAGE_SIZE) {
      return `アップロード可能な画像サイズは${bytesToMB(IMAGE.MAX_ALL_IMAGE_SIZE)}MBまでです`;
    }

    // 音声合計サイズチェック
    const newAudioFilesSize = audioFiles.reduce((acc, file) => acc + file.size, 0);
    const existingAudioFilesSize = existingAudioFiles.reduce((acc, file) => acc + (file.size || 0), 0);
    if (newAudioFilesSize + existingAudioFilesSize > AUDIO.MAX_ALL_AUDIO_SIZE) {
      return `アップロード可能な音声サイズは${bytesToMB(AUDIO.MAX_ALL_AUDIO_SIZE)}MBまでです`;
    }

    // 動画合計サイズチェック
    const newVideoFilesSize = videoFiles.reduce((acc, file) => acc + file.size, 0);
    const existingVideoFilesSize = existingVideoFiles.reduce((acc, file) => acc + (file.size || 0), 0);
    if (newVideoFilesSize + existingVideoFilesSize > VIDEO.MAX_ALL_VIDEO_SIZE) {
      return `アップロード可能な動画サイズは${bytesToMB(VIDEO.MAX_ALL_VIDEO_SIZE)}MBまでです`;
    }
  }

  return null;
};

export const getIsNowOnSale = (onSale?: OnSale) => {
  if (!onSale || !onSale.discountRate) return false;

  const onSaleStartAt = onSale.startAt === 'null' ? null : onSale.startAt;
  const onSaleEndAt = onSale.endAt === 'null' ? null : onSale.endAt;

  // startAtとendAtの両方が設定されている場合
  if (onSaleStartAt && onSaleEndAt) {
    return isNowBetween(onSaleStartAt as string, onSaleEndAt as string);
  }

  // startAtのみ設定されている場合（終了日時なし）
  if (onSaleStartAt && !onSaleEndAt) {
    return !isFuture(onSaleStartAt);
  }

  // endAtのみ設定されている場合（開始日時なし = 即時開始）
  if (!onSaleStartAt && onSaleEndAt) {
    return isFuture(onSaleEndAt);
  }

  return false;
};

export const getIsNotEndForSale = (isNowOnSale: boolean, forSale?: ForSale) => {
  if (!forSale || !forSale.startAt || !forSale.endAt) return false;

  // 販売期間が未来 && セール中 は存在しえない
  const isStartAtFuture = isFuture(forSale.startAt);
  if (isNowOnSale && isStartAtFuture) return false;

  const isNowForSale = isNowBetween(forSale.startAt, forSale.endAt);
  return isNowForSale || isStartAtFuture;
};

export const getOnSaleBadgeText = (endAt: string | Date, badgeLength: number) => {
  const formattedEndAt = moment(new Date(endAt)).format('YYYY/MM/DD HH:mm');

  if (badgeLength === 1) {
    return `〜${formattedEndAt}`;
  } else if (badgeLength === 2) {
    return 'タイムセール';
  }
  return '';
};

export const getForSaleBadgeText = (
  startAt: string,
  endAt: string,
  badgeLength: number,
  within24Hours: boolean,
  isFutureStart: boolean,
  isNowForSale: boolean,
) => {
  const now = moment();
  const start = moment(startAt);
  const end = moment(endAt);

  if (isFutureStart) {
    return `${start.format('YYYY/MM/DD HH:mm')}〜`;
  }

  if (isNowForSale) {
    if (within24Hours) {
      const diffHours = end.diff(now, 'hours');
      const diffMinutes = end.diff(now, 'minutes') % 60;
      return badgeLength === 1 ? `残り${diffHours}時間${diffMinutes}分` : `残り${diffHours}時間`;
    } else {
      return badgeLength === 1 ? `〜${end.format('YYYY/MM/DD HH:mm')}` : '期間限定';
    }
  }

  return '';
};

const isValidDate = (d?: string | null) => !!d && d !== '' && !isNaN(new Date(d).getTime());

export const getIsNowForSale = (forSale?: ForSale): boolean => {
  if (!forSale) return true;
  const { startAt, endAt } = forSale;
  const isStartAtValid = isValidDate(startAt);
  const isEndAtValid = isValidDate(endAt);

  if (!isStartAtValid && !isEndAtValid) return true;
  if (isStartAtValid && isEndAtValid) return isNowBetween(startAt as string, endAt as string);
  if (isStartAtValid) return isBefore(startAt as string);
  return false;
};
