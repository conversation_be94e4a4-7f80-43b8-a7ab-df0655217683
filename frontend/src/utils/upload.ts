'use client';

import { toast } from 'react-hot-toast';
import CustomToast from '@/components/atoms/toast/custom-toast';
import { ShopLimitation } from '@/lib/server-api/shop-api.schemas';
import { THUMBNAIL_BLUR_LEVEL, THUMBNAIL_WATERMARK_LEVEL } from '@/consts/file';
import { MAX_ITEM_COUNT } from '@/consts/sizes';
import { fileService } from '@/services/file';
import { validateUploadedFiles } from '@/utils/item';
import {
  generatePreSignedThumbnails,
  generateProcessedThumbnail,
  generatePreMediaThumbnail,
  generateMediaThumbnail,
} from '@/utils/thumbnail';
import { GachaItemFile, Award, AWARD_TYPE } from '@/types/gacha';
import { ItemFiles } from '@/types/shopItem';

export interface UploadOptions {
  // Common options
  currentFiles: FileList | null;
  existingFiles: ItemFiles;
  shopLimitation: ShopLimitation;
  identityId: string;
  setFiles: (files: ItemFiles) => void | Promise<void>;
  onProgress?: (id: string, progress: number) => void;
  resetProgress?: () => void;

  // Optional options
  isPublic?: boolean;
  setIsLoading?: (isLoading: boolean) => void;

  // Gacha-specific options
  awardType?: Award;
  isGacha?: boolean;
  itemId?: string; // For GachaUploadItems
}

// ファイルアップロードを処理する関数（画像圧縮なし）
const handleMediaUpload = async (
  files: FileList,
  currentFiles: ItemFiles,
  callback: (files: ItemFiles) => void | Promise<void>,
  onProgress?: (id: string, progress: number) => void,
  isPublic?: boolean,
) => {
  const updatedFiles = [...currentFiles];
  const mediaItems = await Promise.all(
    Array.from(files).map(async (file) => {
      try {
        if (file.type.includes('image')) {
          /*************************************
           * 画像のアップロード処理（圧縮処理なし）
           *************************************/
          // ローディング表示のためSingleItemをコールバック
          const preItem = generatePreMediaThumbnail(file);
          updatedFiles.push(preItem);
          if (callback) {
            const result = callback(updatedFiles);
            if (result instanceof Promise) {
              await result;
            }
          }

          let fileUrl = '';

          try {
            fileUrl = URL.createObjectURL(file);
            // 実際にアップロード処理を行う（元の画像をそのままアップロード）
            const uploadUrl = await fileService.uploadFile({
              file: file,
              metadataList: [{ id: preItem.id, name: null }],
              isPublic: isPublic,
              onProgress: (progress) => {
                onProgress?.(preItem.id, progress);
              },
            });

            const result = await generateMediaThumbnail(preItem, uploadUrl, uploadUrl, isPublic);
            return result;
          } catch (error) {
            toast.custom((t) => CustomToast(t, 'error', '画像アップロードに失敗しました'), {
              id: `upload-error-${preItem.id}`,
            });
            throw error;
          } finally {
            // 元の画像を破棄
            if (fileUrl) URL.revokeObjectURL(fileUrl);
          }
        } else {
          /*************************************
           * 画像以外のアップロード処理
           *************************************/
          // ファイルのローディング表示のためSingleItemをコールバック
          const preItem = generatePreMediaThumbnail(file);
          updatedFiles.push(preItem);
          if (callback) {
            const result = callback(updatedFiles);
            if (result instanceof Promise) {
              await result;
            }
          }
          // 実際にアップロード処理を行う
          const uploadUrl = await fileService.uploadFile({
            file: file,
            metadataList: [{ id: preItem.id, name: null }],
            isPublic: isPublic,
            onProgress: (progress) => onProgress?.(preItem.id, progress),
          });
          return generateMediaThumbnail(preItem, uploadUrl, undefined, isPublic);
        }
      } catch (error) {
        console.error('Error processing file:', error);
        toast.custom((t) => CustomToast(t, 'error', '商品アップロードに失敗しました'));
        throw error;
      }
    }),
  );

  return [...currentFiles, ...mediaItems];
};

const handleFileUpload = async (options: UploadOptions): Promise<boolean> => {
  const {
    currentFiles,
    existingFiles,
    shopLimitation,
    identityId,
    setFiles,
    onProgress,
    resetProgress,
    isPublic = false,
    setIsLoading,
    awardType = AWARD_TYPE.S, // Default to S award
    isGacha = false,
  } = options;

  // Validate input
  if (!currentFiles || currentFiles.length === 0) return false;

  // Validate files
  const validationResult = validateUploadedFiles(currentFiles, existingFiles, shopLimitation);
  if (validationResult) {
    toast.custom((t) => CustomToast(t, 'error', validationResult));
    return false;
  }

  // Check if adding these files would exceed the maximum (only for gacha)
  if (isGacha && existingFiles.length + currentFiles.length > MAX_ITEM_COUNT) {
    toast.custom((t) => CustomToast(t, 'error', `アップロードできるファイルは最大${MAX_ITEM_COUNT}個までです`));
    return false;
  }

  // Set loading state if provided
  if (setIsLoading) {
    setIsLoading(true);
  }

  try {
    // Reset progress if provided
    if (resetProgress) {
      resetProgress();
    }

    // For gacha uploads, we use an empty array to avoid duplicating files
    // For regular uploads, we use the existing files
    const initialFiles: ItemFiles = isGacha ? [] : existingFiles;

    // Handle the upload process
    let resultFiles = await handleMediaUpload(
      currentFiles,
      initialFiles,
      async (files) => {
        // This callback is called during upload to show progress
        if (isGacha) {
          // For gacha, we need to merge with existing files for the UI
          // Make sure all new files have isLoading set to true
          const filesWithLoading = files.map((file) => ({
            ...file,
            isLoading: true, // Explicitly set isLoading to true during upload
          }));

          const mergedFiles = [...existingFiles, ...filesWithLoading];
          const result = setFiles(mergedFiles);
          if (result instanceof Promise) {
            await result;
          }
        } else {
          // For regular uploads, we just set the files
          const result = setFiles(files);
          if (result instanceof Promise) {
            await result;
          }
        }
      },
      onProgress,
      isPublic,
    );
    // Generate thumbnails
    resultFiles = await generatePreSignedThumbnails(resultFiles, identityId);
    // For gacha uploads, we need to process thumbnails and set award type
    if (isGacha) {
      resultFiles = await Promise.all(
        resultFiles.map(async (file) => {
          const processed = await generateProcessedThumbnail(
            file as any,
            THUMBNAIL_BLUR_LEVEL.HIGH,
            THUMBNAIL_WATERMARK_LEVEL.WHITE,
          );
          return {
            ...processed,
            awardType, // Set the award type based on the current state
            isSecret: false,
            sortOrder: 0,
            isLoading: false, // Explicitly set isLoading to false when processing is complete
          } as unknown as GachaItemFile;
        }),
      );

      // Update files in store with existing files and new files
      const updatedFiles = [...existingFiles, ...resultFiles];
      const result = setFiles(updatedFiles);
      if (result instanceof Promise) {
        await result;
      }
    } else {
      // For regular uploads, we just set the files
      const result = setFiles(resultFiles);
      if (result instanceof Promise) {
        await result;
      }
    }

    return true;
  } catch (error) {
    console.error('Error processing media files:', error);
    toast.custom((t) => CustomToast(t, 'error', 'ファイルのアップロードに失敗しました'));
    return false;
  } finally {
    // For non-gacha uploads, reset loading state
    // For gacha uploads, the loading state is controlled by the useEffect
    if (setIsLoading && !isGacha) {
      setIsLoading(false);
    }
  }
};

export { handleMediaUpload, handleFileUpload };
