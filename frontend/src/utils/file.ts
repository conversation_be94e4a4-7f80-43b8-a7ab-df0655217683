import toast from 'react-hot-toast';
import CustomToast from '@/components/atoms/toast/custom-toast';
import { fileService } from '@/services/file';
import type { GachaBenefitFile } from '@/types/gacha';
import type { SingleItem } from '@/types/shopItem';

/**
 * get key from utl
 */
export function getS3KeyFromUrl(url: string): string {
  try {
    // URLの最後のパス部分を取得して正規化
    const key = new URL(url).pathname.split('/').pop() || '';
    return key.normalize('NFC');
  } catch (error) {
    console.error('Error getting S3 key from URL:', error);
    return '';
  }
}

/**
 * Download single file
 */
export async function downloadSingleFile(itemId: string, file: SingleItem | GachaBenefitFile): Promise<void> {
  await downloadFiles(itemId, [file]);
}

/**
 * Download multiple files
 */
export async function downloadFiles(itemId: string, files: (SingleItem | GachaBenefitFile)[]): Promise<void> {
  try {
    const downloadUrls = await fileService.getDownloadUrl({
      itemId: parseInt(itemId),
      fileData: files
        .filter((file) => !!file.type)
        .map((item) => ({ item, name: fileService.getValidFileNameForDownload(item.title, item.type!) })),
    });
    downloadUrls.forEach((downloadItem, index) => {
      setTimeout(() => {
        fileService.downloadByLink(downloadItem.url);
      }, index * 200);
    });
  } catch (error) {
    console.error('Error downloading file:', error);
    toast.custom((t) => CustomToast(t, 'error', 'ダウンロードに失敗しました'), {
      id: 'download-failed',
    });
  }
}
