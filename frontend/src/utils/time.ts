import moment from 'moment-timezone';

const formatEndDate = (date: string) => {
  return moment(new Date(date)).format('YYYY/MM/DD HH:mm');
};

const formatRankingDate = (date: string) => {
  return moment(new Date(date)).tz('Asia/Tokyo').format('YYYY/MM/DD HH:mm');
};

const getRemainingTime = (date: string) => {
  const end = moment(new Date(date));
  const now = moment();
  const diffDuration = moment.duration(end.diff(now));
  const hours = Math.floor(diffDuration.asHours());
  const minutes = diffDuration.minutes();
  return `残り${hours.toString().padStart(2, '0')}時間${minutes.toString().padStart(2, '0')}分`;
};

const isWithin24Hours = (date: string) => {
  const end = moment(new Date(date));
  const now = moment();
  const diffHours = end.diff(now, 'hours');
  return diffHours <= 24;
};

const isFuture = (date: Date | string | null | undefined) => {
  if (!date) return false;
  const start = moment(new Date(date));
  const now = moment();
  return start.isAfter(now);
};

const isNowBetween = (startAt: string | Date | null, endAt: string | Date | null): boolean => {
  if (!startAt || !endAt) return false;
  const now = moment();
  const start = moment(startAt);
  const end = moment(endAt);

  return now.isAfter(start) && now.isBefore(end);
};

const isBefore = (date: Date | string | null | undefined) => {
  if (!date) return false;
  const now = moment();
  const target = moment(new Date(date));
  return now.isSameOrAfter(target);
};

export { formatEndDate, formatRankingDate, getRemainingTime, isWithin24Hours, isFuture, isNowBetween, isBefore };
