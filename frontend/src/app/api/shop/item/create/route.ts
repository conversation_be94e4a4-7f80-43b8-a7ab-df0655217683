import { revalidateTag } from 'next/cache';
import { NextRequest, NextResponse } from 'next/server';
import { createAxiosClient } from '@/app/api/_client';
import { apiAuth } from '@/app/api/utils/auth';
import { validateArrayItems, validateFields } from '@/app/api/utils/validation';
import { createItemRules, itemRules } from '@/app/api/utils/validationRules/create';
import { CreateOrUpdateItemRequest } from '@/types/api';
import { ITEM_TYPE } from '@/types/item';

export async function POST(request: NextRequest) {
  const client = createAxiosClient(request);
  const isAuth = await apiAuth();
  if (!isAuth) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  try {
    const requestBody: CreateOrUpdateItemRequest = await request.json();

    // Validate all data
    const validation = validateFields(requestBody, createItemRules);
    if (!validation.isValid) {
      return NextResponse.json({ error: validation.error }, { status: validation.status || 400 });
    }

    // Validate items array
    if (requestBody.itemType !== ITEM_TYPE.CHEKI.value) {
      const itemsValidation = validateArrayItems(requestBody.itemFiles, itemRules, 'files');
      if (!itemsValidation.isValid) {
        return NextResponse.json({ error: itemsValidation.error }, { status: itemsValidation.status || 400 });
      }
    }

    // Transform request data to match backend API format
    const backendRequest = {
      name: requestBody.title,
      description: requestBody.description,
      thumbnailUri: requestBody.thumbnail,
      thumbnailFrom: requestBody.thumbnailFrom,
      thumbnailBlurLevel: requestBody.thumbnailBlurLevel,
      thumbnailWatermarkLevel: requestBody.thumbnailWatermarkLevel,
      price: requestBody.priceSet,
      available: requestBody.available,
      itemFiles: requestBody.itemFiles.map((file, index) => ({
        name: file.title,
        objectUri: file.src,
        thumbnailUri: file.thumbnail,
        price: file.price,
        fileType: file.type,
        size: file.size,
        duration: file.duration || 0,
        itemThumbnailSelected: file.selected,
        sortOrder: index,
      })),
      itemType: requestBody.itemType,
      samples: requestBody.samples
        ? requestBody.samples?.map((sample) => ({
            name: sample.title,
            objectUri: sample.src,
            thumbnailUri: sample.thumbnail,
            fileType: sample.type,
            size: sample.size,
            duration: sample.duration || 0,
          }))
        : null,
      benefits: requestBody.benefits
        ? requestBody.benefits.map((benefit) => ({
            description: benefit.description || '',
            conditionType: 0,
            files: benefit.benefitFiles.map((item) => ({
              name: item.title,
              objectUri: item.src,
              thumbnailUri: item.thumbnail,
              fileType: item.type,
              size: item.size,
              duration: item.duration || 0,
            })),
          }))
        : null,
      tags: requestBody.tags || null,
      itemOption: {
        isSingleSales: requestBody.itemOption?.singleSale,
        qtyTotal: requestBody.itemOption?.limited || null,
        qtyPerUser: requestBody.itemOption?.limitedPerUser || null,
        forSale: requestBody.itemOption?.period
          ? {
              startAt: requestBody.itemOption.period.start ? requestBody.itemOption.period.start + ':00' : null,
              endAt: requestBody.itemOption.period.end ? requestBody.itemOption.period.end + ':00' : null,
            }
          : null,
        password: requestBody.itemOption?.password || null,
        onSale: requestBody.itemOption?.discount
          ? {
              discountRate: requestBody.itemOption.discount.percentage / 100,
              startAt: requestBody.itemOption.discount.start ? requestBody.itemOption.discount.start + ':00' : null,
              endAt: requestBody.itemOption.discount.end ? requestBody.itemOption.discount.end + ':00' : null,
            }
          : null,
      },
    };

    console.log(backendRequest.benefits);

    const response = await client.post(`/shops/current/items`, backendRequest);
    const { identityId } = response.data.data.item;
    revalidateTag(`shop-items-${identityId}`);
    return NextResponse.json(response.data);
  } catch (error: any) {
    // バックエンドからのレスポンスをそのまま返す
    if (error.response?.data) {
      return NextResponse.json(error.response.data, {
        status: error.response.status,
      });
    }

    // ほかの場合は500エラーを返す
    return NextResponse.json(
      {
        data: {},
        errors: [
          {
            code: 500,
            message: 'Failed to process item creation',
          },
        ],
      },
      {
        status: 500,
      },
    );
  }
}
