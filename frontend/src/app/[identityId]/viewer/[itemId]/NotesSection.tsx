'use client';

import React from 'react';
import SectionTitle from '@/components/atoms/typography/section-title';

interface NotesSectionProps {
  isDigital?: boolean;
}

const NotesSection = ({ isDigital = true }: NotesSectionProps) => {
  return (
    <section className="flex w-full flex-col bg-white p-4">
      {isDigital ? (
        <>
          <SectionTitle title="デジタル商品の注意事項" />
          <section className="text-regular-13">
            <h3>【推奨環境】</h3>
            <div>
              <h4>[パソコン環境]</h4>
              <p>Google Chrome</p>
              <p>Safari</p>

              <h4>[スマートフォン環境]</h4>
              <p>Android 最新版のGoogle Chrome</p>
              <p>iOS 最新版のSafari</p>
            </div>

            <h3>【注意事項】</h3>
            <div>
              <p>
                商品ごとで品質が異なる場合がございます。閲覧している通信環境によって、正常に動作しない場合がございます。ご自宅などのインターネット回線、Wi-Fiの接続を推奨いたします。スマートフォンのOS、ブラウザのバージョンは、常に最新のものにアップデートをお願いいたします。
              </p>
            </div>
          </section>
        </>
      ) : (
        <>
          <SectionTitle className="m-0" title="商品について" />
          <section className="text-regular-13">
            <p>【商品について】</p>
            <section>
              <p>・商品の特性上、色味や仕上がりに個体差が生じる場合があります。</p>
              <p>・画像はイメージです。実際の商品と異なる場合がございます。</p>
              <p>・プリント商品は印刷の特性上、わずかなズレやムラが発生することがあります。</p>
              <p>・チェキ等の一点ものは、同じ商品の再販・交換ができません。</p>
            </section>

            <p>【発送・配送について】</p>
            <section>
              <p>・ご注文が集中した場合、発送・配送に遅れが生じる可能性がございます。予めご了承ください。</p>
              <p>・送料は各商品の決済画面に表示された金額となります。</p>
              <p>・ヤマト運輸が提供する、ネコポスでの配送となります。</p>
              <p>・配送中の紛失・破損については責任を負いかねます。</p>
              <p>・海外発送には対応しておりません。ご了承ください。</p>
            </section>

            <p>【返品・交換について】</p>
            <section>
              <p>・商品の特性上、イメージ違いやお客様都合での返品・交換は対応できません。</p>
              <p>・破損や印刷不良があった場合は、商品到着後7日以内にご連絡ください。</p>
            </section>

            <p>【その他注意事項】</p>
            <section>
              <p>・長期保存を希望される場合は、直射日光や高温多湿を避けて保管してください。</p>
              <p>・小さなお子様の手の届かない場所で保管してください。</p>
            </section>
          </section>
        </>
      )}
    </section>
  );
};

export default NotesSection;
