'use client';

import ShopPublicImage from '@/components/ShopImage';

type OnDownload<T> = (file: T) => void;
type BenefitContentProps<T> = {
  item: T;
  onDownload?: OnDownload<T>;
  isShowDownload: boolean;
  type?: string;
  title: string;
};
const BenefitContent = <T,>({ onDownload, item, isShowDownload, type = 'image', title }: BenefitContentProps<T>) => {
  const getIconPath = (type: string) => {
    switch (type) {
      case 'image':
        return '/images/icons/PhotoIcnWhite.svg';
      case 'audio':
        return '/images/icons/VoiceIcnWhite.svg';
      case 'video':
        return '/images/icons/MovieIcnWhite.svg';
      default:
        return '/images/icons/PhotoIcnWhite.svg';
    }
  };
  console.log(isShowDownload);
  return (
    <div className="flex items-center justify-start gap-2">
      <ShopPublicImage src={getIconPath(type)} alt="特典" width={20} height={20} />
      <div className="text-regular-13 text-white">{title}</div>
      {isShowDownload && (
        <div
          className="ml-auto cursor-pointer"
          onClick={(event: React.MouseEvent) => {
            event.stopPropagation();
            onDownload?.(item);
          }}
        >
          <ShopPublicImage src="/images/icons/Download_White.svg" alt="download" width={20} height={20} />
        </div>
      )}
    </div>
  );
};

export default BenefitContent;
