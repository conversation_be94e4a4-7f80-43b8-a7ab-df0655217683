import BenefitContent from './benefit-content';
import type { SingleItem } from '@/types/shopItem';

type BenefitItemProps = {
  item: SingleItem;
  readOnly?: boolean;
  onDownload?: (file: SingleItem) => void;
  handleClick: (id?: string) => void;
  index: number;
  length: number;
};

const getBenefitClass = (index: number, length: number) => {
  return length === 1
    ? 'rounded-m bg-gray-700'
    : index === 0
      ? 'rounded-t-m bg-gray-700'
      : index === length - 1
        ? 'rounded-b-m bg-gray-700'
        : 'bg-gray-700';
};

const BenefitItem = ({ item, readOnly, onDownload, handleClick, index, length }: BenefitItemProps) => {
  const benefitClass = getBenefitClass(index, length);
  return (
    <div
      key={item?.id}
      className={`flex h-10 w-full items-center justify-start gap-2 px-4 ${benefitClass}`}
      onClick={() => !readOnly && handleClick('id' in item ? item.id.toString() : undefined)}
    >
      <BenefitContent
        item={item}
        onDownload={onDownload}
        isShowDownload={!readOnly && !!onDownload}
        type={item.type}
        title={item.title}
      />
    </div>
  );
};

export default BenefitItem;
