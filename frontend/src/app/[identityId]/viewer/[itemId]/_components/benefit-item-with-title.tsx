import BenefitContent from './benefit-content';
import type { GachaBenefitFile, GachaBenefit } from '@/types/gacha';

type BenefitItemProps = {
  title: React.ReactNode;
  length: number;
  item: GachaBenefit;
  readOnly?: boolean;
  onDownload?: (file: GachaBenefitFile) => void;
  handleClick: (id?: string) => void;
  index: number;
};

const BenefitItemWithTitle = ({ title, length, onDownload, handleClick, index, item }: BenefitItemProps) => {
  return (
    <div key={item.id} className={`w-full overflow-hidden rounded-m ${index === length - 1 ? '' : 'mb-2'}`}>
      <div className="flex h-8 w-full items-center justify-start bg-white pl-4">{title}</div>
      {item.benefitFiles.map((benefitFile, index) => {
        const isReadOnly = !benefitFile.src;

        return (
          <div
            key={benefitFile.id}
            className={`bg-secondary p-4 ${
              index === 0 ? '' : index === item.benefitFiles.length - 1 ? 'rounded-b-m' : ''
            }`}
            onClick={() => !isReadOnly && handleClick(benefitFile.id)}
          >
            <BenefitContent
              item={benefitFile}
              onDownload={onDownload}
              isShowDownload={!isReadOnly && !!onDownload}
              type={benefitFile.type}
              title={benefitFile.title}
            />
            {item.description && (
              <div className="mt-2 rounded-md bg-gray-700 px-2 py-3 text-regular-13 text-white">{item.description}</div>
            )}
          </div>
        );
      })}
    </div>
  );
};

export default BenefitItemWithTitle;
