'use client';

import React from 'react';
import { usePlayerStore, PlayerType } from '@/store/usePlayer';
import BenefitItem from './benefit-item';
import BenefitItemWithTitle from './benefit-item-with-title';
import type { GachaBenefit } from '@/types/gacha';
import type { SingleItem, Benefit } from '@/types/shopItem';
import { GACHA_BENEFIT_CONDITIONS } from '@/consts/gacha-data';

interface BenefitItemListProps<T> {
  items: T[];
  readOnly?: boolean;
  onDownload?: (file: T) => void;
  onFileDownload?: (file: any) => void; // For individual file downloads
}

const BenefitItemList = <T extends Benefit | GachaBenefit>({
  items,
  readOnly = true,
  onFileDownload,
}: BenefitItemListProps<T>) => {
  const { setPlayerProps, onPlayerOpen } = usePlayerStore();
  const handleClick = (id?: string) => {
    if (readOnly) return;
    if (!id) return;

    const item = items.find((item: T) => 'id' in item && item.id?.toString() === id);

    if (item && 'src' in item && 'thumbnail' in item && 'type' in item) {
      onPlayerOpen();
      setPlayerProps({
        src: item.src as string,
        thumbnail: item.thumbnail as string,
        type: item.type as PlayerType,
      });
    }
  };
  const benefitTitle = (index: number, conditionTitle: string) => {
    return (
      <div className="flex items-center justify-center gap-2">
        <span className="flex h-5 w-12.5 items-center justify-center rounded-m bg-gray-800 text-medium-11 text-white">
          特典 {index + 1}
        </span>
        <span className="text-medium-12 text-secondary">{conditionTitle}</span>
      </div>
    );
  };
  return (
    <div className="flex flex-col items-center gap-0.5">
      {items.map((item: Benefit | GachaBenefit, index: number) => {
        if (item.conditionType) {
          const conditionTitle = GACHA_BENEFIT_CONDITIONS.find(
            (condition) => condition.value === item.conditionType,
          )?.label;

          return (
            <BenefitItemWithTitle
              title={benefitTitle(index, conditionTitle!)}
              onDownload={onFileDownload}
              handleClick={handleClick}
              index={index}
              key={index}
              item={item as unknown as GachaBenefit}
              length={items.length}
            />
          );
        } else {
          return item.benefitFiles.map((benefitFile: SingleItem, index: number) => (
            <BenefitItem
              key={benefitFile?.id}
              item={benefitFile}
              readOnly={readOnly}
              onDownload={onFileDownload}
              handleClick={handleClick}
              index={index}
              length={item.benefitFiles.length}
            />
          ));
        }
      })}
    </div>
  );
};

export default BenefitItemList;
