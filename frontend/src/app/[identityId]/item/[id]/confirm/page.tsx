'use client';
import { useMemo, useState } from 'react';
import toast from 'react-hot-toast';
import { sendGTMEvent } from '@next/third-parties/google';
import clsx from 'clsx';
import { useParams, useSearchParams } from 'next/navigation';
import StateBadge from '@/components/atoms/badges/state-badge';
import Button from '@/components/atoms/button';
import ItemIcon from '@/components/atoms/itemIcon';
import CustomToast from '@/components/atoms/toast/custom-toast';
import SectionTitle from '@/components/atoms/typography/section-title';
import SectionTitleWithNumber from '@/components/atoms/typography/section-title-with-number';
import BenefitConfirmSection from '@/components/containers/BenefitConfirmSection';
import FlatItem from '@/components/containers/FlatItem';
import GachaContentList from '@/components/containers/GachaContentList';
import UploadedItemList from '@/components/containers/UploadedItemsList';
import ShopPublicImage from '@/components/ShopImage';
import Agreement from '@/components/views/Agreement';
import PreviewSquareItem from '@/components/views/PreviewSquareItem';
import {
  createDigitalGachaItem,
  updateDigitalGachaItem,
} from '@/lib/client-api/current-user-digital-gacha-endpoint/current-user-digital-gacha-endpoint';
import { useCurrentUser } from '@/store/useCurrentUser';
import { useExhibitsStore } from '@/store/useExhibit';
import { useModalsStore } from '@/store/useModals';
import { roboto } from '@/app/fonts';
import { THUMBNAIL_BLUR_LEVEL, THUMBNAIL_WATERMARK_LEVEL } from '@/consts/file';
import { shopItemsRequests } from '@/services/shopItemsRequests';
import { formatDateTime, resetEscapedValue } from '@/utils/base';
import { toCreateDigitalGachaItemRequest, toUpdateDigitalGachaItemRequest } from '@/utils/gacha-helpers';
import { isDigitalBundle, isDigitalGacha, isPhysicalItem } from '@/utils/itemTypes';
import { uploadThumbnail } from '@/utils/thumbnail';
import { CreateOrUpdateItemRequest } from '@/types/api';
import { GachaItemFile } from '@/types/gacha';
import { getItemTypeValueFromStr, ITEM_TYPE } from '@/types/item';
import { ExhibitType, ItemFiles, SingleItem } from '@/types/shopItem';

export default function ConfirmPage() {
  const { exhibits, reset } = useExhibitsStore();

  const { onModalOpen, onModalClose, setModalProps } = useModalsStore();

  const params = useParams();
  const searchParams = useSearchParams();
  const itemType = (searchParams.get('item_type') || ITEM_TYPE.DIGITAL_BUNDLE.str) as ExhibitType;

  const itemId = params.id as string;

  const [checked, setChecked] = useState(false);
  const { currentUser } = useCurrentUser();
  const exhibitItem = useMemo(() => {
    return exhibits.find((exhibit) => exhibit.itemId === itemId);
  }, [exhibits, itemId]);
  const isDigitalBundleItem = isDigitalBundle(exhibitItem);
  const isDigitalGachaItem = isDigitalGacha(exhibitItem);
  const {
    thumbnail = '',
    thumbnailCustomImage = '',
    title = '',
    priceSet = 0,
    available = false,
    thumbnailRatio = 1,
    description = '',
    samples = [],
    limited = 0,
    period,
    discount,
    benefits,
    thumbnailType,
    thumbnailBlurLevel,
    thumbnailWatermarkLevel,
  } = exhibitItem || {};
  const singleSale = isDigitalBundleItem ? (exhibitItem.singleSale ?? false) : false;
  const isEditableItemType = isDigitalBundleItem || isDigitalGachaItem;
  const itemFiles: ItemFiles = isEditableItemType ? (exhibitItem.itemFiles ?? []) : [];
  const gachaItemFiles: GachaItemFile[] = isDigitalGachaItem ? (exhibitItem.itemFiles ?? []) : [];

  const limitedPerUser = isPhysicalItem(exhibitItem) ? exhibitItem?.limitedPerUser : undefined;
  const isDuplicated = isDigitalGacha(exhibitItem) ? exhibitItem?.isDuplicated : false;
  const itemPassword = isDigitalGacha(exhibitItem) ? undefined : exhibitItem?.password;

  const finalBenefits = useMemo(() => {
    return benefits && benefits.length > 0
      ? benefits
          .filter((benefit) => benefit.id !== 0) // filter掉所有id为0的benefit
          .map((benefit) => ({
            description: benefit.description || '',
            conditionType: benefit.conditionType,
            benefitFiles: benefit?.benefitFiles.filter((file) => file.id !== 'empty'),
          }))
          .filter((benefit) => benefit?.benefitFiles.length > 0)
      : undefined;
  }, [benefits]);

  const handleCopyUrl = (textToCopy: string) => {
    navigator.clipboard
      ?.writeText(textToCopy)
      .then(() => {
        toast.custom((t) => CustomToast(t, 'success', '商品のURLをコピーしました'), { id: 'copy-url-success' });
      })
      .catch(() => {
        toast.custom((t) => CustomToast(t, 'error', '商品のURLをコピーに失敗しました'), { id: 'copy-url-failed' });
      });
  };

  const handleTwitterShare = (exhibitId?: string) => {
    const url = `https://${window.location.hostname}/shop/@${currentUser?.accountIdentity}/item/${exhibitId ?? itemId}`;
    const descriptor = isDigitalBundleItem
      ? 'デジタル商品'
      : isDigitalGachaItem
        ? 'デジタルガチャ'
        : itemType === ITEM_TYPE.CHEKI.str
          ? 'インスタント写真'
          : 'デジタル商品';
    const text = `FANME SHOPで${descriptor}を出品しました！\n感想くれたらうれしいな〜！\n\n👇👇から購入できます！\n${url}\n\n#FANME #ファンミー #FANMEshop #新作\n@FANME__officialより`;
    const twitterUrl = `https://twitter.com/intent/tweet?text=${encodeURIComponent(text)}`;

    window.open(twitterUrl, '_blank');
  };

  const exhibitSuccessDialogbody = (exhibitId?: string) => (
    <div className="px-2 pb-5 pt-8">
      <h5 className="mb-8 text-center text-bold-17 text-orange-200">商品が出品されました</h5>
      <FlatItem
        id={Number(itemId)}
        imageUrl={thumbnail!}
        title={title!}
        isSet={true}
        price={priceSet!}
        thumbnailRatio={thumbnailRatio!}
        files={itemFiles?.map((item) => ({
          ...item,
          type: item.type || 'image',
        }))}
        showRemoveButton={false}
      />
      <div className="mt-8 flex items-center justify-between rounded-lg border border-gray-800 px-3 py-2">
        <div className={clsx(roboto.className, 'w-71 overflow-hidden text-medium-16')}>
          https://{window.location.hostname}/shop/@{currentUser?.accountIdentity}/item/{exhibitId ?? itemId}
        </div>
        <div
          className="cursor-pointer"
          onClick={() => {
            handleCopyUrl(
              `https://${window.location.hostname}/shop/@${currentUser?.accountIdentity}/item/${exhibitId ?? itemId}`,
            );
          }}
        >
          <ShopPublicImage src="/images/icons/Copy.svg" width={20} height={20} alt="コピー" />
        </div>
      </div>
      <div className="flex items-center justify-center">
        <Button
          className="my-6"
          buttonType={'dark-small'}
          buttonSize={'mdPlus'}
          onClick={() => handleTwitterShare(exhibitId)}
        >
          <ShopPublicImage
            src="/images/icons/Twitter.svg"
            alt="TwitterShare"
            width={20}
            height={20}
            className={'mr-1.5'}
          />
          シェアする
        </Button>
      </div>
    </div>
  );
  const handleCloseExhibitModal = () => {
    onModalClose();
    reset(itemId);
    // 編集後、反映されないことがあるので、pushで遷移せずに、location.replaceでページごとリロードする形で遷移
    window.location.replace(`/shop/@${currentUser?.accountIdentity}?editMode=true`);
  };
  const handleExhibitSuccess = (id?: string) => {
    onModalOpen();
    setModalProps({
      size: 'lg',
      titleType: 'warning',
      onClose: handleCloseExhibitModal,
      title: '出品完了',
      children: exhibitSuccessDialogbody(id),
    });
  };
  const exhibitErrorDialogbody = <div className="p-6 pt-11 text-center">商品出品失敗しました</div>;

  const handleExhibitFailure = () => {
    onModalOpen();
    setModalProps({
      onClose: onModalClose,
      type: 'error',
      children: exhibitErrorDialogbody,
    });
  };
  const handleSubmit = async () => {
    if (!thumbnail) {
      toast.custom((t) => CustomToast(t, 'error', '商品のサムネイルが設定されていません'), { id: 'thumbnail-not-set' });
      return;
    }

    if (itemType === ITEM_TYPE.CHEKI.str) {
      if (!limitedPerUser) {
        toast.custom((t) => CustomToast(t, 'error', '最大購入数が設定されていません'), { id: 'limited-not-set' });
        return;
      }
      if (!period?.start || !period?.end) {
        toast.custom((t) => CustomToast(t, 'error', '販売期間が設定されていません'), { id: 'period-not-set' });
        return;
      }
    }

    // サムネイルを商品から選んだ場合はここでアップロード
    let thumbnailUrl: string;
    if (thumbnailType === 'custom' && thumbnailCustomImage) {
      thumbnailUrl = thumbnailCustomImage;
    } else {
      const blurLevel = itemFiles.find((item) => item.selected)?.blur ?? THUMBNAIL_BLUR_LEVEL.NONE;
      const watermarkLevel = itemFiles.find((item) => item.selected)?.watermark ?? THUMBNAIL_WATERMARK_LEVEL.WHITE;

      const uploadedThumbnail = await uploadThumbnail({
        img: thumbnail,
        fileId: itemId,
        fileName: resetEscapedValue(
          `thumbnail_${title}_${new Date().getTime()}_blur_${blurLevel}_watermark_${watermarkLevel}`,
        ),
        isPublic: true,
      });
      thumbnailUrl = uploadedThumbnail.url;
    }
    // 単品販売商品の価格を更新（バックエンドではnullの場合は単品販売ではないと判断される）
    let uploadItemFiles = [...itemFiles];
    uploadItemFiles = uploadItemFiles.map((file) => {
      if (!singleSale || !file.isSingleSale) {
        return { ...file, price: undefined };
      }
      return file;
    });

    const finalSamples = samples.filter((item) => item.id !== 'empty');

    const requestData: CreateOrUpdateItemRequest & { itemId: string } = {
      // id?: number;
      // description?: BenefitDescription;
      // conditionType?: number;
      // files: BenefitFile1[];
      itemId: itemId.startsWith('new') ? '' : itemId,
      thumbnailFrom: thumbnailType === 'upload' ? 1 : 0,
      thumbnailBlurLevel: thumbnailBlurLevel ?? THUMBNAIL_BLUR_LEVEL.NONE,
      thumbnailWatermarkLevel: thumbnailWatermarkLevel ?? THUMBNAIL_WATERMARK_LEVEL.WHITE,
      title,
      description,
      thumbnail: thumbnailUrl,
      priceSet,
      available,
      itemFiles: uploadItemFiles,
      itemType: getItemTypeValueFromStr(itemType),
      samples: finalSamples.length > 0 ? finalSamples : undefined,
      benefits: finalBenefits && finalBenefits.length > 0 ? finalBenefits : undefined,
      // tags: [],
      itemOption: {
        singleSale,
        limited: limited || undefined,
        limitedPerUser: limitedPerUser || undefined,
        period: period
          ? {
              start: period.start ? formatDateTime(period.start, 'display') : undefined,
              end: period.end ? formatDateTime(period.end, 'display') : undefined,
            }
          : undefined,
        password: itemPassword || undefined,
        discount: discount
          ? {
              percentage: discount.percentage,
              start: discount.start ? formatDateTime(discount.start, 'display') : undefined,
              end: discount.end ? formatDateTime(discount.end, 'display') : undefined,
            }
          : undefined,
      },
    };

    try {
      if (itemId.startsWith('new') && isDigitalGachaItem) {
        const digitalGachaRequest = toCreateDigitalGachaItemRequest(exhibitItem);

        try {
          const response = await createDigitalGachaItem(digitalGachaRequest);
          handleExhibitSuccess(response.data?.item?.id?.toString());
          sendGTMEvent({ event: 'fanme_listing_gacha_create_success', item_id: itemId });
        } catch (error) {
          console.error('Error creating digital gacha item:', error);
          handleExhibitFailure();
        }
      } else if (!itemId.startsWith('new') && isDigitalGachaItem) {
        const digitalGachaRequest = toUpdateDigitalGachaItemRequest(exhibitItem);

        try {
          const response = await updateDigitalGachaItem(Number(itemId), digitalGachaRequest);
          handleExhibitSuccess(response.data?.item?.id?.toString());
          sendGTMEvent({ event: 'fanme_listing_gacha_update_success', item_id: itemId });
        } catch (error) {
          console.error('Error updating digital gacha item:', error);
          handleExhibitFailure();
        }
      } else if (itemId.startsWith('new') && !isDigitalGachaItem) {
        console.log(requestData);

        const response = await shopItemsRequests.createItem(requestData);

        if (response.statusText === 'success') {
          const { item } = response.data;
          const { id } = item;

          handleExhibitSuccess(id);

          sendGTMEvent({ event: 'fanme_listing_item_create_success', item_id: id });
        }
      } else {
        const response = await shopItemsRequests.editItem(requestData);
        if (response.statusText === 'success') {
          handleExhibitSuccess();

          sendGTMEvent({ event: 'fanme_listing_item_edit_success', item_id: itemId });
        }
      }
    } catch (error: any) {
      console.error('Error creating item:', error.error);
      handleExhibitFailure();
      // TODO: Show error message to user
    }
  };

  const PeriodSection = () => {
    return (
      <>
        <SectionTitle className="mt-6" title="販売期間" />
        {(!!period?.start || !!period?.end) && (
          <div className="mt-4 flex flex-col gap-4">
            {period?.start && (
              <div className="flex items-center gap-x-3">
                <StateBadge type="round-filled" color="white" size="sm" position="center">
                  販売開始
                </StateBadge>
                <span className={clsx(roboto.className, 'text-medium-18')}>
                  {formatDateTime(period?.start, 'display')}
                </span>
              </div>
            )}
            {period?.end && (
              <div className="flex items-center gap-x-3">
                <StateBadge type="round-filled" color="white" size="sm" position="center">
                  販売終了
                </StateBadge>
                <span className={clsx(roboto.className, 'text-medium-18')}>
                  {formatDateTime(period?.end, 'display')}
                </span>
              </div>
            )}
          </div>
        )}
      </>
    );
  };

  return (
    <div>
      <section className="section-double-border p-4">
        <SectionTitleWithNumber numbering="01" title="商品データ" />
        {isDigitalBundleItem ? (
          <SectionTitle className="mt-6" title="セット商品" />
        ) : isDigitalGachaItem ? (
          <SectionTitle className="mt-6" title="デジタルガチャ" />
        ) : itemType === ITEM_TYPE.CHEKI.str ? (
          <SectionTitle className="mt-6" title="チェキ" />
        ) : null}
        <FlatItem
          itemType={itemType === ITEM_TYPE.DIGITAL_BUNDLE.str ? 0 : itemType === ITEM_TYPE.DIGITAL_GACHA.str ? 1 : 2}
          id={Number(itemId)}
          imageUrl={thumbnail!}
          title={title!}
          isSet={true}
          price={priceSet}
          thumbnailRatio={thumbnailRatio!}
          files={itemFiles?.map((file) => ({
            ...file,
            type: file.type || 'image',
          }))}
          showRemoveButton={false}
        />
        {singleSale && <SectionTitle className="mt-6" title="単品商品" />}
        {singleSale && itemFiles.length > 0 && <UploadedItemList isLoading={false} />}
        {isDigitalGachaItem && (
          <>
            <SectionTitle className="mt-6" title="ガチャ内容" />
            <GachaContentList
              itemFiles={gachaItemFiles}
              awardProbabilities={isDigitalGachaItem && isDuplicated ? exhibitItem?.awardProbabilities : undefined}
              totalCapacity={isDigitalGachaItem ? exhibitItem?.totalCapacity : 0}
            />
            <SectionTitle className="mt-6" title="景品の重複設定" />
            <div className="mt-4 rounded-lg border border-gray-300 bg-white p-4">
              <p className="text-medium-14">{isDuplicated ? '同じ景品が重複して出現する' : '同じ景品が重複しない'}</p>
            </div>
          </>
        )}
      </section>
      <section className="section-double-border p-4">
        <SectionTitleWithNumber numbering="02" title="商品タイトル" />
        <SectionTitle className="mt-6" title="商品タイトル" />
        <h3 className="mt-4 overflow-hidden text-ellipsis break-words text-bold-18">{title}</h3>
        {description && (
          <>
            <SectionTitle className="mt-6" title="商品説明" />
            <p className="mt-4 overflow-hidden text-ellipsis whitespace-pre-wrap break-words text-regular-14">
              {description}
            </p>
          </>
        )}
        {/* <SectionTitle className="mt-6" title="関連タグ" /> */}
        {/* <div className="flex flex-wrap gap-2">
        {tags?.map((tag) => (
          <Tag key={tag} isEditable={false}>
            {tag}
          </Tag>
        ))}
      </div> */}
      </section>
      <section className="section-double-border p-4">
        <SectionTitleWithNumber numbering="03" title="商品の表紙画像(サムネイル)" />
        {!isDigitalGachaItem && <SectionTitle className="mt-6" title="サムネイル" />}
        <div className="flex items-center justify-center">
          <ItemIcon
            thumbnail={thumbnail!}
            thumbnailRatio={thumbnailRatio!}
            title={title!}
            size={352}
            radius={isDigitalGachaItem ? 'full' : '2xl'}
          />
        </div>
      </section>
      {!!samples[0]?.title && (
        <section className="section-double-border p-4">
          <SectionTitleWithNumber numbering="04" title="サンプルデータ" />
          <div className="mt-4 grid grid-cols-3 items-start gap-4 PC:grid-cols-4 PC:gap-2">
            {samples.map((sample) => sample.title && <PreviewSquareItem key={sample.title} item={sample} />)}
          </div>
        </section>
      )}
      {finalBenefits && finalBenefits.length > 0 && (
        <section className="section-double-border p-4">
          <SectionTitleWithNumber numbering="05" title="購入特典" />
          <BenefitConfirmSection isGacha={isDigitalGachaItem} benefits={finalBenefits} />
        </section>
      )}
      {itemType === ITEM_TYPE.CHEKI.str && (
        <section className="section-double-border p-4">
          <SectionTitleWithNumber numbering="06" title="購入設定" />
          <PeriodSection />
          <SectionTitle className="mt-6" title="最大購入数" />
          <p className="mt-4 text-bold-22">{limitedPerUser?.toLocaleString()}</p>
        </section>
      )}
      {limited || period?.start || period?.end || discount?.percentage || itemPassword ? (
        <section className="section-double-border p-4">
          <SectionTitleWithNumber
            numbering={itemType === ITEM_TYPE.CHEKI.str ? '07' : isDigitalGachaItem ? '05' : '06'}
            title="オプション設定"
          />
          {!!limited && (
            <>
              <SectionTitle className="mt-6" title="販売個数" />
              <p className="mt-4 text-bold-22">{limited.toLocaleString()}</p>
            </>
          )}
          {(period?.start || period?.end) && itemType !== ITEM_TYPE.CHEKI.str && <PeriodSection />}
          {!!discount?.percentage && (
            <>
              <SectionTitle className="mt-6" title="セール販売設定" />
              <div className="mt-4 flex items-center gap-4">
                <StateBadge type="round-filled" color="white" size="sm" position="center">
                  割引率
                </StateBadge>
                <p className={clsx(roboto.className, 'text-medium-18')}>{discount.percentage}%</p>
              </div>
              {(!!discount?.start || !!discount?.end) && (
                <div className="mt-4 flex flex-col gap-4">
                  {discount?.start && (
                    <div className="flex items-center gap-x-3">
                      <StateBadge type="round-filled" color="white" size="sm" position="center">
                        セール開始
                      </StateBadge>
                      <span className={clsx(roboto.className, 'text-medium-18')}>
                        {formatDateTime(discount?.start, 'display')}
                      </span>
                    </div>
                  )}
                  {discount?.end && (
                    <div className="flex items-center gap-x-3">
                      <StateBadge type="round-filled" color="white" size="sm" position="center">
                        セール終了
                      </StateBadge>
                      <span className={clsx(roboto.className, 'text-medium-18')}>
                        {formatDateTime(discount?.end, 'display')}
                      </span>
                    </div>
                  )}
                </div>
              )}
            </>
          )}
          {!!itemPassword && !isDigitalGachaItem && (
            <>
              <SectionTitle className="mt-6" title="パスワード" />
              <p className={clsx(roboto.className, 'mt-4 text-medium-18')}>{itemPassword}</p>
            </>
          )}
        </section>
      ) : null}
      <section className="section-double-border p-4">
        <SectionTitleWithNumber
          numbering={itemType === ITEM_TYPE.CHEKI.str ? '08' : isDigitalGachaItem ? '06' : '07'}
          title="公開設定"
        />
        <p className="mt-4 text-medium-16">{available ? '公開する' : '非公開にする'}</p>
      </section>

      <Agreement checked={checked} setChecked={setChecked} />
      <div className="grid place-items-center">
        <Button
          className="mb-22 mt-10"
          buttonType={checked ? 'dark' : 'disabled'}
          onClick={handleSubmit}
          disabled={!checked}
        >
          出品する
        </Button>
      </div>
    </div>
  );
}
