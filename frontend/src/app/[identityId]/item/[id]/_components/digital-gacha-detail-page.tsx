import DigitalGachaDetailWrapper from './digital-gacha-detail-wrapper';
import type { GachaItem } from '@/types/gacha';

type DigitalGachaDetailProps = {
  data: GachaItem;
  itemId: string;
  identityId: string;
  shopName: string;
  creatorName: string;
};

const DigitalGachaDetail = ({ data, itemId, identityId, shopName, creatorName }: DigitalGachaDetailProps) => {
  const {
    description,
    limited,
    period,
    itemFiles,
    thumbnail,
    thumbnailRatio,
    remainingAmount,
    title,
    price,
    currentPrice,
    discountRate,
    benefits,
    samples,
    discount,
    isPurchased,
    isCheckout,
    hasPassword,
    totalCapacity,
    awardProbabilities,
    collectedUniqueItemsCount,
    duplicatedCount,
    available,
    itemType,
    isCompleted,
    isDuplicated,
    remainingUniquePullCount,
  } = data.item;

  const isSoldOut = remainingAmount === 0;

  const thumbnailProps = {
    shopName,
    creatorName,
    itemId,
    itemFiles,
    samples,
    title,
    priceSet: price,
    currentPrice,
    thumbnailRatio,
    thumbnail,
    discount,
    isPreview: false,
    identityId,
    itemType,
  };
  const pullGachaProps = {
    description,
    price,
    currentPrice,
    discount,
    period,
    available: !!available,
    itemId,
    identityId,
    isCompleted,
    isDuplicated,
    remainingUniquePullCount,
  };
  const gachaMainInfoProps = {
    description,
    limited,
    period,
    itemId,
    totalPulledCount: itemFiles.reduce((sum, item) => sum + (item.receivedFileCount || 0), 0),
  };
  const gachaItemsSectionProps = {
    itemFiles,
    thumbnail,
    title,
    price,
    currentPrice,
    discountRate,
    thumbnailRatio,
    discount,
    benefits,
    identityId,
    itemId: Number(itemId),
    isPurchased,
    isCheckout,
    period,
    isSoldOut,
    hasPassword,
    forceShowItemOption: false,
    totalCapacity,
    awardProbabilities,
  };
  const gachaCompleteSectionProps = {
    isPreview: false,
    totalGachaCount: itemFiles.length,
    collectedUniqueItemsCount,
    isCompleted,
    thumbnail,
    itemId: Number(itemId),
  };
  const progressBarProps = {
    itemFiles,
    duplicatedCount,
    isPreview: false,
  };

  return (
    <DigitalGachaDetailWrapper
      thumbnailProps={thumbnailProps}
      pullGachaProps={pullGachaProps}
      gachaMainInfoProps={gachaMainInfoProps}
      gachaItemsSectionProps={gachaItemsSectionProps}
      gachaCompleteSectionProps={gachaCompleteSectionProps}
      progressBarProps={progressBarProps}
    />
  );
};

export default DigitalGachaDetail;
