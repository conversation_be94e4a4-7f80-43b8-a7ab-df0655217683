'use client';

import toast from 'react-hot-toast';
import CustomToast from '@/components/atoms/toast/custom-toast';
import Instruction from '@/components/atoms/typography/instruction';
import ShopPublicImage from '@/components/ShopImage';
import { getDownloadUrl } from '@/lib/client-api/file-endpoint/file-endpoint';
import { Benefit, DownloadUrl, BenefitFile } from '@/lib/server-api/shop-api.schemas';
import BenefitItemList from '@/app/[identityId]/purchased-item/[purchasedItemId]/benefit-item-list';
import { fileService } from '@/services/file';
import { getS3KeyFromUrl } from '@/utils/file';
import { MediaType } from '@/types/shopItem';

interface BenefitsSectionProps {
  itemId: string;
  benefits: Benefit[];
}

const BenefitsSection = ({ itemId, benefits }: BenefitsSectionProps) => {
  const onSingleDownloadFile = async (file: BenefitFile) => {
    console.log(file);

    // await downloadFiles([file]);
  };

  // const downloadFiles = async (files: BenefitFile[]) => {
  //   try {
  //     const downloadUrls = await getDownloadUrl({
  //       itemId: parseInt(itemId),
  //       metadataList: files
  //         .filter((file) => !!file.fileType)
  //         .map((file) => {
  //           console.log(file);

  //           if (!file.objectUri) return;
  //           return {
  //             key: getS3KeyFromUrl(file.objectUri),
  //             name: fileService.getValidFileNameForDownload(file.name, file.fileType! as MediaType),
  //           };
  //         }),
  //     });
  //     downloadUrls.data?.downloadUrls.forEach((downloadItem: DownloadUrl, index: number) => {
  //       setTimeout(() => {
  //         fileService.downloadByLink(downloadItem.url);
  //       }, index * 200);
  //     });
  //   } catch (error) {
  //     console.error('Error downloading file:', error);
  //     toast.custom((t) => CustomToast(t, 'error', 'ダウンロードに失敗しました'), {
  //       id: 'download-failed',
  //     });
  //   }
  // };

  return (
    <section className="flex w-full flex-col gap-4 bg-gray-100 px-4">
      <div className="flex w-full flex-col gap-3 rounded-2xl bg-yellow-50 p-4">
        <div className="flex items-center justify-between gap-1">
          <div className="flex items-center gap-1">
            <ShopPublicImage src="/images/icons/GiftOrange.svg" alt="セット購入特典" width={22} height={22} />
            <h2 className="text-medium-16 text-orange-200">セット購入特典</h2>
          </div>
          <Instruction className="text-red-500">* 各特典クリックで内容確認</Instruction>
        </div>
        {benefits[0].description && (
          <div className="whitespace-pre-wrap rounded-2xl bg-white p-2 text-regular-13">{benefits[0].description}</div>
        )}
        {benefits[0].files && benefits[0].files.length > 0 && (
          <BenefitItemList items={benefits[0].files} readOnly={false} onDownload={onSingleDownloadFile} />
        )}
      </div>
    </section>
  );
};

export default BenefitsSection;
