/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * backend API
 * OpenAPI spec version: 1.0.0-SNAPSHOT
 */
export type AgenciesResponseBodyData = AgencyData | null;

export type AgenciesResponseBodyErrors = ErrorObject[] | null;

export interface AgenciesResponseBody {
  data: AgenciesResponseBodyData;
  errors?: AgenciesResponseBodyErrors;
}

export type AgencyId = number | null;

export type AgencyCreatedAt = Instant | null;

export type AgencyUpdatedAt = Instant | null;

export type AgencyDeletedAt = Instant | null;

export interface Agency {
  id?: AgencyId;
  createdAt: AgencyCreatedAt;
  updatedAt: AgencyUpdatedAt;
  name: string;
  deletedAt?: AgencyDeletedAt;
}

export interface AgencyData {
  agencies: Agency[];
}

export interface AgencySales {
  agencySales: CreatorSales[];
}

export type AgencySalesResponseBodyData = AgencySales | null;

export type AgencySalesResponseBodyErrors = ErrorObject[] | null;

export interface AgencySalesResponseBody {
  data: AgencySalesResponseBodyData;
  errors?: AgencySalesResponseBodyErrors;
}

export interface ApplePayParam {
  token: string;
}

export type AssetType = (typeof AssetType)[keyof typeof AssetType];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const AssetType = {
  IMAGE: 'IMAGE',
  VOICE: 'VOICE',
  MOVIE: 'MOVIE',
  ANY: 'ANY',
} as const;

export type AuditGroupId = number | null;

export type AuditGroupCreatedAt = Instant | null;

export type AuditGroupUpdatedAt = Instant | null;

export type AuditGroupUser = User | null;

export type AuditGroupMetadataProperty = string | null;

export type AuditGroupComment = string | null;

export type AuditGroupAuditedAt = LocalDateTime | null;

/**
 * @maxLength 50
 */
export type AuditGroupAuditedUserUid = string | null;

export type AuditGroupMetadataObject = AuditGroupMetadata | null;

export interface AuditGroup {
  id?: AuditGroupId;
  createdAt: AuditGroupCreatedAt;
  updatedAt: AuditGroupUpdatedAt;
  auditObjects: AuditObject[];
  /** @maxLength 50 */
  userUid: string;
  user?: AuditGroupUser;
  auditType: AuditType;
  operationType: OperationType;
  metadata?: AuditGroupMetadataProperty;
  status: AuditStatus;
  comment?: AuditGroupComment;
  auditedAt?: AuditGroupAuditedAt;
  /** @maxLength 50 */
  auditedUserUid?: AuditGroupAuditedUserUid;
  metadataObject?: AuditGroupMetadataObject;
}

/**
 * ショップID
 */
export type AuditGroupMetadataShopId = number | null;

/**
 * 商品ID
 */
export type AuditGroupMetadataItemId = string | null;

/**
 * タイトル
 */
export type AuditGroupMetadataTitle = string | null;

/**
 * 説明
 */
export type AuditGroupMetadataDescription = string | null;

export interface AuditGroupMetadata {
  /** ショップID */
  shopId?: AuditGroupMetadataShopId;
  /** 商品ID */
  itemId?: AuditGroupMetadataItemId;
  /** タイトル */
  title?: AuditGroupMetadataTitle;
  /** 説明 */
  description?: AuditGroupMetadataDescription;
}

export interface AuditGroupsData {
  auditGroups: AuditGroup[];
}

export type AuditGroupsResponseBodyData = AuditGroupsData | null;

export type AuditGroupsResponseBodyErrors = ErrorObject[] | null;

export interface AuditGroupsResponseBody {
  data: AuditGroupsResponseBodyData;
  errors?: AuditGroupsResponseBodyErrors;
}

export type AuditObjectId = number | null;

export type AuditObjectCreatedAt = Instant | null;

export type AuditObjectUpdatedAt = Instant | null;

export type AuditObjectAuditGroup = AuditGroup | null;

export interface AuditObject {
  id?: AuditObjectId;
  createdAt: AuditObjectCreatedAt;
  updatedAt: AuditObjectUpdatedAt;
  auditGroup?: AuditObjectAuditGroup;
  auditGroupId: number;
  /** @maxLength 50 */
  bucket: string;
  /** @maxLength 255 */
  filePath: string;
  assetType: AssetType;
}

export type AuditStatus = (typeof AuditStatus)[keyof typeof AuditStatus];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const AuditStatus = {
  UNAUDITED: 'UNAUDITED',
  REJECTED: 'REJECTED',
  PENDING: 'PENDING',
  RESEND: 'RESEND',
  APPROVED: 'APPROVED',
} as const;

export interface AuditStatusData {
  result?: boolean;
}

export type AuditStatusResponseBodyData = AuditStatusData | null;

export type AuditStatusResponseBodyErrors = ErrorObject[] | null;

export interface AuditStatusResponseBody {
  data: AuditStatusResponseBodyData;
  errors?: AuditStatusResponseBodyErrors;
}

export type AuditType = (typeof AuditType)[keyof typeof AuditType];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const AuditType = {
  SHOP: 'SHOP',
  SHOP_ITEM: 'SHOP_ITEM',
  FANME_PROFILE: 'FANME_PROFILE',
  FANME_CONTENT: 'FANME_CONTENT',
} as const;

export interface AwardProbability {
  awardType?: number;
  probability?: number;
}

export interface AwardProbability1 {
  awardType?: number;
  probability?: number;
}

export interface BadgeRankingData {
  ranking: GetDigitalGachaCompleteBadgeRankingResponse[];
}

export type BadgeRankingResponseBodyData = BadgeRankingData | null;

export type BadgeRankingResponseBodyErrors = ErrorObject[] | null;

export interface BadgeRankingResponseBody {
  data: BadgeRankingResponseBodyData;
  errors?: BadgeRankingResponseBodyErrors;
}

export type BaseResponseBodyErrors = ErrorObject[] | null;

export interface BaseResponseBody {
  data?: unknown;
  errors?: BaseResponseBodyErrors;
}

export type BenefitDescription = string | null;

export interface Benefit {
  id?: number;
  description?: BenefitDescription;
  conditionType?: number;
  files: BenefitFile1[];
}

export type BenefitFileId = number | null;

export type BenefitFileObjectUri = string | null;

export type BenefitFileThumbnailUri = string | null;

export type BenefitFileDuration = number | null;

export type BenefitFileItemThumbnailSelected = boolean | null;

export type BenefitFileSortOrder = number | null;

export interface BenefitFile {
  id?: BenefitFileId;
  name: string;
  objectUri?: BenefitFileObjectUri;
  thumbnailUri?: BenefitFileThumbnailUri;
  fileType: string;
  size?: number;
  duration?: BenefitFileDuration;
  itemThumbnailSelected?: BenefitFileItemThumbnailSelected;
  sortOrder?: BenefitFileSortOrder;
}

export type BenefitFile1ThumbnailUri = string | null;

export type BenefitFile1ItemThumbnailSelected = boolean | null;

export type BenefitFile1SortOrder = number | null;

export interface BenefitFile1 {
  name: string;
  objectUri: string;
  thumbnailUri?: BenefitFile1ThumbnailUri;
  fileType: string;
  size?: number;
  duration?: number;
  itemThumbnailSelected?: BenefitFile1ItemThumbnailSelected;
  sortOrder?: BenefitFile1SortOrder;
  conditionType?: number;
}

export type BenefitParamDescription = string | null;

export type BenefitParamFiles = BenefitFile[] | null;

export interface BenefitParam {
  id?: number;
  description?: BenefitParamDescription;
  conditionType?: number;
  files?: BenefitParamFiles;
}

export interface CardParam {
  cardSequence?: number;
}

export type CartItemFileId = number | null;

export type CartItemFileType = string | null;

export type CartItemFileQuantities = FileQuantity[] | null;

export type CartItemPurchasableQuantity = number | null;

export type CartItemPurchaserComment = string | null;

export interface CartItem {
  cartItemId?: number;
  cartId?: number;
  itemType?: number;
  itemId?: number;
  fileId?: CartItemFileId;
  quantity?: number;
  name: string;
  thumbnailUri: string;
  price?: number;
  marginRate?: number;
  currentPrice?: number;
  discountRate?: number;
  fileType?: CartItemFileType;
  fileQuantities?: CartItemFileQuantities;
  forSale?: boolean;
  soldOut?: boolean;
  purchasableQuantity?: CartItemPurchasableQuantity;
  purchaserComment?: CartItemPurchaserComment;
}

export type CartItemDataDeliveryFee = number | null;

export interface CartItemData {
  cartItems: CartItem[];
  deliveryFee?: CartItemDataDeliveryFee;
  isLocked?: boolean;
}

export type CartItemsResponseBodyErrors = ErrorObject[] | null;

export interface CartItemsResponseBody {
  cart: CartItemData;
  errors?: CartItemsResponseBodyErrors;
}

export interface CheckCartItemPriceRequest {
  itemPrices: ItemPriceSet[];
}

export type CheckoutStatus = (typeof CheckoutStatus)[keyof typeof CheckoutStatus];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const CheckoutStatus = {
  UNPROCESSED: 'UNPROCESSED',
  REQSUCCESS: 'REQSUCCESS',
  PAYSUCCESS: 'PAYSUCCESS',
  EXPIRED: 'EXPIRED',
  CANCEL: 'CANCEL',
  PAYFAILED: 'PAYFAILED',
} as const;

export interface CompleteBadgeData {
  badge: Output1;
}

export type CompleteBadgeResponseBodyData = CompleteBadgeData | null;

export type CompleteBadgeResponseBodyErrors = ErrorObject[] | null;

export interface CompleteBadgeResponseBody {
  data: CompleteBadgeResponseBodyData;
  errors?: CompleteBadgeResponseBodyErrors;
}

export type ConsoleUserId = number | null;

export type ConsoleUserCreatedAt = Instant | null;

export type ConsoleUserUpdatedAt = Instant | null;

export type ConsoleUserAgencyId = number | null;

export type ConsoleUserAgency = Agency | null;

export type ConsoleUserDeletedAt = Instant | null;

export interface ConsoleUser {
  id?: ConsoleUserId;
  createdAt: ConsoleUserCreatedAt;
  updatedAt: ConsoleUserUpdatedAt;
  user: User;
  agencyId?: ConsoleUserAgencyId;
  agency?: ConsoleUserAgency;
  role: string;
  deletedAt?: ConsoleUserDeletedAt;
}

export interface ConsoleUserData {
  user: ConsoleUserDetail;
}

export type ConsoleUserDetailUid = string | null;

export type ConsoleUserDetailIsPublic = boolean | null;

export type ConsoleUserDetailBirthday = Instant | null;

export type ConsoleUserDetailBirthdayConfirmed = boolean | null;

export type ConsoleUserDetailIsBirthdayWeek = number | null;

export type ConsoleUserDetailGender = string | null;

export type ConsoleUserDetailIcon = string | null;

export type ConsoleUserDetailFilledProfile = boolean | null;

export type ConsoleUserDetailAllowPublicSharing = boolean | null;

export type ConsoleUserDetailPurpose = number | null;

export type ConsoleUserDetailRole = string | null;

export type ConsoleUserDetailAgencyId = number | null;

export type ConsoleUserDetailLivecommerceSalesAmount = number | null;

export type ConsoleUserDetailTotalGiftAmount = number | null;

export type ConsoleUserDetailTotalTipAmount = number | null;

export interface ConsoleUserDetail {
  id?: number;
  uid?: ConsoleUserDetailUid;
  name: string;
  accountIdentity: string;
  isPublic?: ConsoleUserDetailIsPublic;
  birthday?: ConsoleUserDetailBirthday;
  birthdayConfirmed?: ConsoleUserDetailBirthdayConfirmed;
  isBirthdayWeek?: ConsoleUserDetailIsBirthdayWeek;
  gender?: ConsoleUserDetailGender;
  icon?: ConsoleUserDetailIcon;
  filledProfile?: ConsoleUserDetailFilledProfile;
  allowPublicSharing?: ConsoleUserDetailAllowPublicSharing;
  purpose?: ConsoleUserDetailPurpose;
  role?: ConsoleUserDetailRole;
  agencyId?: ConsoleUserDetailAgencyId;
  livecommerceSalesAmount?: ConsoleUserDetailLivecommerceSalesAmount;
  totalGiftAmount?: ConsoleUserDetailTotalGiftAmount;
  totalTipAmount?: ConsoleUserDetailTotalTipAmount;
}

export type ConsoleUserResponseBodyData = ConsoleUserData | null;

export type ConsoleUserResponseBodyErrors = ErrorObject[] | null;

export interface ConsoleUserResponseBody {
  data?: ConsoleUserResponseBodyData;
  errors?: ConsoleUserResponseBodyErrors;
}

export interface ConsoleUsersData {
  consoleUsers: ConsoleUser[];
}

export type ConsoleUsersResponseBodyData = ConsoleUsersData | null;

export type ConsoleUsersResponseBodyErrors = ErrorObject[] | null;

export interface ConsoleUsersResponseBody {
  data: ConsoleUsersResponseBodyData;
  errors?: ConsoleUsersResponseBodyErrors;
}

export interface ContentBlockData {
  contentBlocks: GetContentBlockContentBlock[];
}

export type ContentBlockResponseBodyData = ContentBlockData | null;

export type ContentBlockResponseBodyErrors = ErrorObject[] | null;

export interface ContentBlockResponseBody {
  data: ContentBlockResponseBodyData;
  errors?: ContentBlockResponseBodyErrors;
}

export interface ConvenienceParam {
  convenience: string;
  customerName: string;
  customerKana: string;
  telNo: string;
}

export type CreateCartItemRequestSingleFile = number | null;

export interface CreateCartItemRequest {
  itemId?: number;
  singleFile?: CreateCartItemRequestSingleFile;
  quantity?: number;
}

export type CreateContentWithDetailRequestDescription = string | null;

export type CreateContentWithDetailRequestAppDescription = string | null;

export type CreateContentWithDetailRequestIconUrl = string | null;

export interface CreateContentWithDetailRequest {
  contentBlockType?: number;
  title: string;
  description?: CreateContentWithDetailRequestDescription;
  appDescription?: CreateContentWithDetailRequestAppDescription;
  url: string;
  iconUrl?: CreateContentWithDetailRequestIconUrl;
}

export type CreateDigitalGachaItemRequestDescription = string | null;

export type CreateDigitalGachaItemRequestSamples = DigitalGachaSampleFile[] | null;

export type CreateDigitalGachaItemRequestBenefits = Benefit[] | null;

export type CreateDigitalGachaItemRequestTags = string[] | null;

export interface CreateDigitalGachaItemRequest {
  name: string;
  description?: CreateDigitalGachaItemRequestDescription;
  thumbnailUri: string;
  thumbnailFrom?: number;
  thumbnailBlurLevel?: number;
  thumbnailWatermarkLevel?: number;
  price?: number;
  available?: boolean;
  itemFiles: DigitalGachaFile[];
  samples?: CreateDigitalGachaItemRequestSamples;
  benefits?: CreateDigitalGachaItemRequestBenefits;
  tags?: CreateDigitalGachaItemRequestTags;
  itemOption: ItemOption1;
  isDuplicated?: boolean;
  awardProbabilities: AwardProbability1[];
}

export interface CreateItemPasswordUnlockCacheRequest {
  userInputPassword: string;
}

export type CreateOrUpdateItemRequestDescription = string | null;

export type CreateOrUpdateItemRequestSamples = SampleFile[] | null;

export type CreateOrUpdateItemRequestBenefits = Benefit[] | null;

export type CreateOrUpdateItemRequestTags = string[] | null;

export type CreateOrUpdateItemRequestItemType = number | null;

export interface CreateOrUpdateItemRequest {
  name: string;
  description?: CreateOrUpdateItemRequestDescription;
  thumbnailUri: string;
  thumbnailFrom?: number;
  thumbnailBlurLevel?: number;
  thumbnailWatermarkLevel?: number;
  price?: number;
  available?: boolean;
  itemFiles: File1[];
  samples?: CreateOrUpdateItemRequestSamples;
  benefits?: CreateOrUpdateItemRequestBenefits;
  tags?: CreateOrUpdateItemRequestTags;
  itemOption: ItemOption1;
  itemType?: CreateOrUpdateItemRequestItemType;
}

export type CreateOrderRequestCardParam = CardParam | null;

export type CreateOrderRequestConvenienceParam = ConvenienceParam | null;

export type CreateOrderRequestGooglePayParam = GooglePayParam | null;

export type CreateOrderRequestApplePayParam = ApplePayParam | null;

export interface CreateOrderRequest {
  cartId?: number;
  cartItemIds: number[];
  tip?: number;
  paymentMethod: string;
  cardParam?: CreateOrderRequestCardParam;
  convenienceParam?: CreateOrderRequestConvenienceParam;
  googlePayParam?: CreateOrderRequestGooglePayParam;
  applePayParam?: CreateOrderRequestApplePayParam;
}

export type CreateShopRequestDescription = string | null;

export type CreateShopRequestHeaderImageUri = string | null;

export type CreateShopRequestMessage = string | null;

export interface CreateShopRequest {
  name: string;
  description?: CreateShopRequestDescription;
  headerImageUri?: CreateShopRequestHeaderImageUri;
  message?: CreateShopRequestMessage;
}

export type CreateSingleOrderRequestCardParam = CardParam | null;

export type CreateSingleOrderRequestConvenienceParam = ConvenienceParam | null;

export type CreateSingleOrderRequestGooglePayParam = GooglePayParam | null;

export type CreateSingleOrderRequestApplePayParam = ApplePayParam | null;

export interface CreateSingleOrderRequest {
  itemId?: number;
  quantity?: number;
  tip?: number;
  paymentMethod: string;
  cardParam?: CreateSingleOrderRequestCardParam;
  convenienceParam?: CreateSingleOrderRequestConvenienceParam;
  googlePayParam?: CreateSingleOrderRequestGooglePayParam;
  applePayParam?: CreateSingleOrderRequestApplePayParam;
}

export interface CreatorSales {
  creatorUid: string;
  creatorName: string;
  accumulatedSales?: number;
  withdrawableAmount?: number;
  monthlySalesList: MonthlySales[];
}

export type DigitalGachaBenefitFileForUpdateThumbnailUri = string | null;

export type DigitalGachaBenefitFileForUpdateItemThumbnailSelected = boolean | null;

export type DigitalGachaBenefitFileForUpdateSortOrder = number | null;

export interface DigitalGachaBenefitFileForUpdate {
  id?: number;
  name: string;
  objectUri: string;
  thumbnailUri?: DigitalGachaBenefitFileForUpdateThumbnailUri;
  fileType: string;
  size?: number;
  duration?: number;
  itemThumbnailSelected?: DigitalGachaBenefitFileForUpdateItemThumbnailSelected;
  sortOrder?: DigitalGachaBenefitFileForUpdateSortOrder;
}

export type DigitalGachaBenefitForUpdateId = number | null;

export type DigitalGachaBenefitForUpdateDescription = string | null;

export interface DigitalGachaBenefitForUpdate {
  id?: DigitalGachaBenefitForUpdateId;
  description?: DigitalGachaBenefitForUpdateDescription;
  files: DigitalGachaBenefitFileForUpdate[];
}

export type DigitalGachaFileId = number | null;

export type DigitalGachaFileThumbnailUri = string | null;

export type DigitalGachaFileMaskedThumbnailUri = string | null;

export type DigitalGachaFilePrice = number | null;

export type DigitalGachaFileItemThumbnailSelected = boolean | null;

export type DigitalGachaFileSortOrder = number | null;

export type DigitalGachaFileIsSecret = boolean | null;

export interface DigitalGachaFile {
  id?: DigitalGachaFileId;
  name: string;
  objectUri: string;
  thumbnailUri?: DigitalGachaFileThumbnailUri;
  maskedThumbnailUri?: DigitalGachaFileMaskedThumbnailUri;
  price?: DigitalGachaFilePrice;
  fileType: string;
  size?: number;
  duration?: number;
  itemThumbnailSelected?: DigitalGachaFileItemThumbnailSelected;
  sortOrder?: DigitalGachaFileSortOrder;
  awardType?: number;
  isSecret?: DigitalGachaFileIsSecret;
}

export interface DigitalGachaFileForUpdate {
  id?: number;
  name: string;
  isSecret?: boolean;
}

export interface DigitalGachaPullData {
  files: FileForPullDigitalGachaItems[];
}

export type DigitalGachaPullResponseBodyData = DigitalGachaPullData | null;

export type DigitalGachaPullResponseBodyErrors = ErrorObject[] | null;

export interface DigitalGachaPullResponseBody {
  data: DigitalGachaPullResponseBodyData;
  errors?: DigitalGachaPullResponseBodyErrors;
}

export interface DigitalGachaPullableCountData {
  item: Output;
}

export type DigitalGachaPullableCountResponseBodyData = DigitalGachaPullableCountData | null;

export type DigitalGachaPullableCountResponseBodyErrors = ErrorObject[] | null;

export interface DigitalGachaPullableCountResponseBody {
  data: DigitalGachaPullableCountResponseBodyData;
  errors?: DigitalGachaPullableCountResponseBodyErrors;
}

export type DigitalGachaSampleFileThumbnailUri = string | null;

export interface DigitalGachaSampleFile {
  name: string;
  objectUri: string;
  thumbnailUri?: DigitalGachaSampleFileThumbnailUri;
  fileType: string;
  size?: number;
  duration?: number;
}

export type DigitalGachaSampleFileForUpdateId = number | null;

export type DigitalGachaSampleFileForUpdateThumbnailUri = string | null;

export interface DigitalGachaSampleFileForUpdate {
  id?: DigitalGachaSampleFileForUpdateId;
  name: string;
  objectUri: string;
  thumbnailUri?: DigitalGachaSampleFileForUpdateThumbnailUri;
  fileType: string;
  size?: number;
  duration?: number;
}

export interface DownloadUrl {
  key: string;
  url: string;
}

export interface DownloadUrlData {
  downloadUrls: DownloadUrl[];
}

export type DownloadUrlResponseBodyData = DownloadUrlData | null;

export type DownloadUrlResponseBodyErrors = ErrorObject[] | null;

export interface DownloadUrlResponseBody {
  data: DownloadUrlResponseBodyData;
  errors?: DownloadUrlResponseBodyErrors;
}

export interface ErrorObject {
  code: number;
  message: string;
}

export interface EventData {
  event: RankingEventWithBoost;
}

export interface FanmeCustomerData {
  fanmeCustomer: FanmeCustomerEntity;
}

export type FanmeCustomerEntityCreatorUid = string | null;

export type FanmeCustomerEntityBuilding = string | null;

export interface FanmeCustomerEntity {
  creatorUid?: FanmeCustomerEntityCreatorUid;
  firstName: string;
  lastName: string;
  firstNameKana: string;
  lastNameKana: string;
  postalCode: string;
  prefecture: string;
  city: string;
  street: string;
  building?: FanmeCustomerEntityBuilding;
  phoneNumber: string;
}

export type FanmeCustomerResponseBodyData = FanmeCustomerData | null;

export type FanmeCustomerResponseBodyErrors = ErrorObject[] | null;

export interface FanmeCustomerResponseBody {
  data?: FanmeCustomerResponseBodyData;
  errors?: FanmeCustomerResponseBodyErrors;
}

export type FileId = number | null;

export type FileObjectUri = string | null;

export type FileThumbnailUri = string | null;

export type FileMaskedThumbnailUri = string | null;

export type FilePrice = number | null;

export type FileCurrentPrice = number | null;

export type FileDuration = number | null;

export type FileAwardType = number | null;

export type FileReceivedFileCount = number | null;

export interface File {
  id?: FileId;
  name: string;
  objectUri?: FileObjectUri;
  thumbnailUri?: FileThumbnailUri;
  maskedThumbnailUri?: FileMaskedThumbnailUri;
  price?: FilePrice;
  currentPrice?: FileCurrentPrice;
  fileType: string;
  size?: number;
  duration?: FileDuration;
  isPurchased?: boolean;
  isCheckout?: boolean;
  itemThumbnailSelected?: boolean;
  awardType?: FileAwardType;
  isSecret?: boolean;
  conditionType?: number;
  receivedFileCount?: FileReceivedFileCount;
}

export type File1Price = number | null;

export type File1ItemThumbnailSelected = boolean | null;

export interface File1 {
  name: string;
  objectUri: string;
  thumbnailUri: string;
  price?: File1Price;
  fileType: string;
  size?: number;
  duration?: number;
  itemThumbnailSelected?: File1ItemThumbnailSelected;
  sortOrder?: number;
}

export type FileForPullDigitalGachaItemsObjectUri = string | null;

export type FileForPullDigitalGachaItemsThumbnailUri = string | null;

export type FileForPullDigitalGachaItemsDuration = number | null;

export type FileForPullDigitalGachaItemsIsSecret = boolean | null;

export interface FileForPullDigitalGachaItems {
  id?: number;
  name: string;
  objectUri?: FileForPullDigitalGachaItemsObjectUri;
  thumbnailUri?: FileForPullDigitalGachaItemsThumbnailUri;
  fileType: string;
  size?: number;
  duration?: FileForPullDigitalGachaItemsDuration;
  awardType?: number;
  isSecret?: FileForPullDigitalGachaItemsIsSecret;
}

export interface FileQuantity {
  fileType: string;
  quantity?: number;
}

export interface FinalizeCreditCard3DSecureRequest {
  transactionId?: number;
  checkoutId?: number;
}

export type ForSaleStartAt = string | null;

export type ForSaleEndAt = string | null;

export interface ForSale {
  startAt?: ForSaleStartAt;
  endAt?: ForSaleEndAt;
}

export type ForSaleDataStartAt = string | null;

export type ForSaleDataEndAt = string | null;

export interface ForSaleData {
  startAt?: ForSaleDataStartAt;
  endAt?: ForSaleDataEndAt;
}

export interface GetContentBlockContentBlock {
  id?: number;
  contentBlockDetails: GetContentBlockContentBlockDetail[];
  contentBlockType?: number;
  displayOrderNumber?: number;
  displayable?: boolean;
}

export type GetContentBlockContentBlockDetailUrl = string | null;

export type GetContentBlockContentBlockDetailDescription = string | null;

export type GetContentBlockContentBlockDetailAppDescription = string | null;

export type GetContentBlockContentBlockDetailIcon = string | null;

export type GetContentBlockContentBlockDetailStyleAnyOf = { [key: string]: unknown };

export type GetContentBlockContentBlockDetailStyle = GetContentBlockContentBlockDetailStyleAnyOf | null;

export interface GetContentBlockContentBlockDetail {
  id?: number;
  contentGroupNumber?: number;
  isSetIcon?: boolean;
  title: string;
  url?: GetContentBlockContentBlockDetailUrl;
  description?: GetContentBlockContentBlockDetailDescription;
  appDescription?: GetContentBlockContentBlockDetailAppDescription;
  icon?: GetContentBlockContentBlockDetailIcon;
  style?: GetContentBlockContentBlockDetailStyle;
}

export interface GetDigitalGachaCompleteBadgeRankingResponse {
  userUid: string;
  userAccountIdentity: string;
  userName: string;
  userIcon: string;
  getBadgeAt: string;
}

export interface GetDownloadUrlRequest {
  metadataList: MetadataForGetDownloadUrlRequest[];
  itemId?: number;
}

export interface GetPreSignedUrlRequest {
  metadataList: MetadataForGetPreSignedUrlRequest[];
  creatorAccountIdentity: string;
}

export interface GetUploadUrlRequest {
  metadataList: MetadataForGetUploadUrlRequest[];
}

export interface GooglePayParam {
  token: string;
}

export type Instant = string;

export type ItemDescription = string | null;

export type ItemAwardProbabilities = AwardProbability[] | null;

export type ItemIsDuplicatedDigitalGachaItems = boolean | null;

export type ItemFiles = File[] | null;

export type ItemSamples = File[] | null;

export type ItemBenefits = BenefitParam[] | null;

export type ItemTags = string[] | null;

export type ItemRemainingUniquePullCount = number | null;

export interface Item {
  id?: number;
  creatorAccountIdentity: string;
  name: string;
  description?: ItemDescription;
  thumbnailUri: string;
  thumbnailFrom?: number;
  thumbnailBlurLevel?: number;
  thumbnailWatermarkLevel?: number;
  price?: number;
  currentPrice?: number;
  fileType: string;
  available?: boolean;
  awardProbabilities?: ItemAwardProbabilities;
  isDuplicatedDigitalGachaItems?: ItemIsDuplicatedDigitalGachaItems;
  itemType?: number;
  files?: ItemFiles;
  samples?: ItemSamples;
  benefits?: ItemBenefits;
  tags?: ItemTags;
  itemOption: OptionData;
  isPurchased?: boolean;
  isCheckout?: boolean;
  purchasedCount?: number;
  collectedUniqueItemsCount?: number;
  isCompleted?: boolean;
  remainingUniquePullCount?: ItemRemainingUniquePullCount;
}

export interface ItemData {
  item: Item;
}

export type ItemOptionPassword = string | null;

export type ItemOptionOnSale = OnSale | null;

export type ItemOptionForSale = ForSale | null;

export interface ItemOption {
  password?: ItemOptionPassword;
  onSale?: ItemOptionOnSale;
  forSale?: ItemOptionForSale;
}

export type ItemOption1QtyTotal = number | null;

export type ItemOption1QtyPerUser = number | null;

export type ItemOption1ForSale = ForSale | null;

export type ItemOption1Password = string | null;

export type ItemOption1OnSale = OnSale | null;

export interface ItemOption1 {
  isSingleSales?: boolean;
  qtyTotal?: ItemOption1QtyTotal;
  qtyPerUser?: ItemOption1QtyPerUser;
  forSale?: ItemOption1ForSale;
  password?: ItemOption1Password;
  onSale?: ItemOption1OnSale;
}

export interface ItemPriceSet {
  cartItemId?: number;
  displayedPrice?: number;
}

export type ItemResponseBodyData = ItemData | null;

export type ItemResponseBodyErrors = ErrorObject[] | null;

export interface ItemResponseBody {
  data: ItemResponseBodyData;
  errors?: ItemResponseBodyErrors;
}

export type ItemType = (typeof ItemType)[keyof typeof ItemType];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const ItemType = {
  DIGITAL_BUNDLE: 'DIGITAL_BUNDLE',
  DIGITAL_GACHA: 'DIGITAL_GACHA',
  CHEKI: 'CHEKI',
} as const;

export type LocalDateTime = string;

export type MetadataForGetDownloadUrlRequestName = string | null;

export interface MetadataForGetDownloadUrlRequest {
  key: string;
  name?: MetadataForGetDownloadUrlRequestName;
}

export interface MetadataForGetPreSignedUrlRequest {
  id: string;
  key: string;
}

export type MetadataForGetUploadUrlRequestName = string | null;

export interface MetadataForGetUploadUrlRequest {
  id: string;
  name?: MetadataForGetUploadUrlRequestName;
}

export type MonthlySalesExpirationDate = Instant | null;

export interface MonthlySales {
  yearMonth: string;
  sellerSalesAmount?: number;
  merged?: boolean;
  expirationDate?: MonthlySalesExpirationDate;
}

export type OnSaleStartAt = string | null;

export type OnSaleEndAt = string | null;

export interface OnSale {
  discountRate?: number;
  startAt?: OnSaleStartAt;
  endAt?: OnSaleEndAt;
}

export type OnSaleDataStartAt = string | null;

export type OnSaleDataEndAt = string | null;

export interface OnSaleData {
  discountRate?: number;
  startAt?: OnSaleDataStartAt;
  endAt?: OnSaleDataEndAt;
}

export type OperationType = (typeof OperationType)[keyof typeof OperationType];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const OperationType = {
  INSERT: 'INSERT',
  UPDATE: 'UPDATE',
} as const;

export type OptionDataQtyTotal = number | null;

export type OptionDataQtyPerUser = number | null;

export type OptionDataRemainingAmount = number | null;

export type OptionDataForSale = ForSaleData | null;

export type OptionDataPassword = string | null;

export type OptionDataOnSale = OnSaleData | null;

export interface OptionData {
  isSingleSales?: boolean;
  qtyTotal?: OptionDataQtyTotal;
  qtyPerUser?: OptionDataQtyPerUser;
  remainingAmount?: OptionDataRemainingAmount;
  forSale?: OptionDataForSale;
  password?: OptionDataPassword;
  onSale?: OptionDataOnSale;
}

export type OrderId = number | null;

export type OrderCreatedAt = Instant | null;

export type OrderUpdatedAt = Instant | null;

export type OrderTransactionId = number | null;

export type OrderCheckoutId = number | null;

export interface Order {
  id?: OrderId;
  createdAt: OrderCreatedAt;
  updatedAt: OrderUpdatedAt;
  /** @pattern \S */
  purchaserUid: string;
  shop: Shop1;
  transactionId?: OrderTransactionId;
  checkoutId?: OrderCheckoutId;
}

export type OrderResultPurchasedItems = PurchasedItem[] | null;

export type OrderResultConvenienceCheckout = Output2 | null;

export type OrderResultRedirectUrl = string | null;

export interface OrderResult {
  order: Order;
  purchasedItems?: OrderResultPurchasedItems;
  convenienceCheckout?: OrderResultConvenienceCheckout;
  redirectUrl?: OrderResultRedirectUrl;
}

export interface OrderedItem {
  id?: number;
  name: string;
  itemType?: number;
  price?: number;
  marginRate?: number;
  quantity?: number;
}

export interface Output {
  itemId?: number;
  remainingPullCount?: number;
}

export type Output1Rank = number | null;

export interface Output1 {
  itemId?: number;
  isAcquired?: boolean;
  rank?: Output1Rank;
}

export type Output2ReceiptUrl = string | null;

export interface Output2 {
  checkoutId?: number;
  convenience: string;
  confNo: string;
  receiptNo: string;
  paymentTerm: string;
  receiptUrl?: Output2ReceiptUrl;
  status: CheckoutStatus;
}

export interface PullDigitalGachaRequest {
  itemId?: number;
}

export interface PurchaseItemData {
  purchasedItem: PurchasedItemDetail;
}

export type PurchaseItemResponseBodyData = PurchaseItemData | null;

export type PurchaseItemResponseBodyErrors = ErrorObject[] | null;

export interface PurchaseItemResponseBody {
  data: PurchaseItemResponseBodyData;
  errors?: PurchaseItemResponseBodyErrors;
}

export type PurchasedItemId = number | null;

export type PurchasedItemCreatedAt = Instant | null;

export type PurchasedItemUpdatedAt = Instant | null;

export type PurchasedItemItemId = number | null;

export type PurchasedItemItemCreatedAt = Instant | null;

export type PurchasedItemItemUpdatedAt = Instant | null;

/**
 * @maxLength 800
 */
export type PurchasedItemItemDescription = string | null;

export type PurchasedItemItem = {
  id?: PurchasedItemItemId;
  createdAt: PurchasedItemItemCreatedAt;
  updatedAt: PurchasedItemItemUpdatedAt;
  /**
   * @minLength 1
   * @maxLength 100
   * @pattern \S
   */
  name: string;
  /** @maxLength 800 */
  description?: PurchasedItemItemDescription;
  /** @pattern \S */
  thumbnailUri: string;
  /**
   * @minimum 0
   * @maximum 1
   */
  thumbnailFrom: number;
  /**
   * @minimum 0
   * @maximum 2
   */
  thumbnailBlurLevel: number;
  /**
   * @minimum 0
   * @maximum 2
   */
  thumbnailWatermarkLevel: number;
  /**
   * @minimum 100
   * @maximum 1000000
   */
  price: number;
  fileType: number;
  available: boolean;
  marginRate: number;
  sortOrder: number;
  itemType: ItemType;
  digital?: boolean;
};

export type PurchasedItemItemFileAnyOfId = number | null;

export type PurchasedItemItemFileAnyOfCreatedAt = Instant | null;

export type PurchasedItemItemFileAnyOfUpdatedAt = Instant | null;

/**
 * @pattern \S
 */
export type PurchasedItemItemFileAnyOfObjectUri = string | null;

export type PurchasedItemItemFileAnyOfThumbnailUri = string | null;

export type PurchasedItemItemFileAnyOfMaskedThumbnailUri = string | null;

/**
 * @minimum 0
 * @maximum 1000000
 */
export type PurchasedItemItemFileAnyOfPrice = number | null;

export type PurchasedItemItemFileAnyOf = {
  id?: PurchasedItemItemFileAnyOfId;
  createdAt: PurchasedItemItemFileAnyOfCreatedAt;
  updatedAt: PurchasedItemItemFileAnyOfUpdatedAt;
  /**
   * @maxLength 30
   * @pattern \S
   */
  name: string;
  /** @pattern \S */
  objectUri: PurchasedItemItemFileAnyOfObjectUri;
  thumbnailUri?: PurchasedItemItemFileAnyOfThumbnailUri;
  maskedThumbnailUri?: PurchasedItemItemFileAnyOfMaskedThumbnailUri;
  /**
   * @minimum 0
   * @maximum 1000000
   */
  price?: PurchasedItemItemFileAnyOfPrice;
  /** @pattern \S */
  fileType: string;
  size?: number;
  /** @minimum 0 */
  duration?: number;
  itemThumbnailSelected: boolean;
  sortOrder: number;
};

export type PurchasedItemItemFile = PurchasedItemItemFileAnyOf | null;

export type PurchasedItemPurchaserComment = string | null;

export type PurchasedItemPurchasedAt = Instant | null;

export interface PurchasedItem {
  id?: PurchasedItemId;
  createdAt: PurchasedItemCreatedAt;
  updatedAt: PurchasedItemUpdatedAt;
  order: Order;
  /** @pattern \S */
  purchaserUid: string;
  item: PurchasedItemItem;
  itemFile?: PurchasedItemItemFile;
  /**
   * @minimum 100
   * @maximum 1000000
   */
  price?: number;
  /** @minimum 1 */
  quantity?: number;
  purchaserComment?: PurchasedItemPurchaserComment;
  /** @pattern \S */
  status: string;
  purchasedAt?: PurchasedItemPurchasedAt;
}

export type PurchasedItemCheckoutPaymentType = string | null;

export type PurchasedItemCheckoutConvenience = string | null;

export type PurchasedItemCheckoutConfNo = string | null;

export type PurchasedItemCheckoutReceiptNo = string | null;

export type PurchasedItemCheckoutPaymentTerm = string | null;

export type PurchasedItemCheckoutReceiptUrl = string | null;

export type PurchasedItemCheckoutStatus = string | null;

export type PurchasedItemCheckoutTotal = number | null;

export type PurchasedItemCheckoutTipAmount = number | null;

export type PurchasedItemCheckoutCvsFee = number | null;

export type PurchasedItemCheckoutDeliveryFee = number | null;

export interface PurchasedItemCheckout {
  paymentType?: PurchasedItemCheckoutPaymentType;
  convenience?: PurchasedItemCheckoutConvenience;
  confNo?: PurchasedItemCheckoutConfNo;
  receiptNo?: PurchasedItemCheckoutReceiptNo;
  paymentTerm?: PurchasedItemCheckoutPaymentTerm;
  receiptUrl?: PurchasedItemCheckoutReceiptUrl;
  status?: PurchasedItemCheckoutStatus;
  total?: PurchasedItemCheckoutTotal;
  tipAmount?: PurchasedItemCheckoutTipAmount;
  cvsFee?: PurchasedItemCheckoutCvsFee;
  deliveryFee?: PurchasedItemCheckoutDeliveryFee;
}

export type PurchasedItemDetailCheckout = PurchasedItemCheckout | null;

export type PurchasedItemDetailPurchaserComment = string | null;

export interface PurchasedItemDetail {
  id?: number;
  itemId?: number;
  purchasedAt: string;
  order: PurchasedItemOrder;
  checkout?: PurchasedItemDetailCheckout;
  purchaserComment?: PurchasedItemDetailPurchaserComment;
}

export interface PurchasedItemOrder {
  id?: number;
  orderNumber: string;
  items: OrderedItem[];
  orderedAt: string;
}

export type RankingEventId = number | null;

export type RankingEventCreatedAt = Instant | null;

export type RankingEventUpdatedAt = Instant | null;

/**
 * @maxLength 255
 */
export type RankingEventEventIdentity = string | null;

/**
 * @maxLength 255
 */
export type RankingEventName = string | null;

export type RankingEventDescription = string | null;

/**
 * @maxLength 255
 */
export type RankingEventImageUrl = string | null;

/**
 * @maxLength 255
 */
export type RankingEventBaseColor = string | null;

/**
 * @maxLength 255
 */
export type RankingEventAddInfos = string | null;

/**
 * @maxLength 255
 */
export type RankingEventJudgeX = string | null;

/**
 * @maxLength 255
 */
export type RankingEventJudgeInstagram = string | null;

/**
 * @maxLength 255
 */
export type RankingEventShareHashTags = string | null;

/**
 * @maxLength 255
 */
export type RankingEventResults = string | null;

export type RankingEventApplyStartAt = Instant | null;

export type RankingEventApplyEndAt = Instant | null;

export type RankingEventStartAt = Instant | null;

export type RankingEventEndAt = Instant | null;

export type RankingEventCalculatedAt = Instant | null;

export type RankingEventArchivedAt = Instant | null;

export interface RankingEvent {
  id?: RankingEventId;
  createdAt: RankingEventCreatedAt;
  updatedAt: RankingEventUpdatedAt;
  /** @maxLength 255 */
  eventIdentity: RankingEventEventIdentity;
  /** @maxLength 255 */
  name: RankingEventName;
  description: RankingEventDescription;
  /** @maxLength 255 */
  imageUrl: RankingEventImageUrl;
  /** @maxLength 255 */
  baseColor?: RankingEventBaseColor;
  /** @maxLength 255 */
  addInfos?: RankingEventAddInfos;
  /** @maxLength 255 */
  judgeX?: RankingEventJudgeX;
  /** @maxLength 255 */
  judgeInstagram?: RankingEventJudgeInstagram;
  /** @maxLength 255 */
  shareHashTags?: RankingEventShareHashTags;
  /** @maxLength 255 */
  results?: RankingEventResults;
  applyStartAt: RankingEventApplyStartAt;
  applyEndAt: RankingEventApplyEndAt;
  startAt: RankingEventStartAt;
  endAt: RankingEventEndAt;
  calculatedAt: RankingEventCalculatedAt;
  archivedAt: RankingEventArchivedAt;
}

export type RankingEventInfoResponseBodyData = EventData | null;

export type RankingEventInfoResponseBodyErrors = ErrorObject[] | null;

export interface RankingEventInfoResponseBody {
  data: RankingEventInfoResponseBodyData;
  errors?: RankingEventInfoResponseBodyErrors;
}

export type RankingEventWithBoostRankingEvent = RankingEvent | null;

export type RankingEventWithBoostBoost = RankingYellBoost | null;

export interface RankingEventWithBoost {
  rankingEvent?: RankingEventWithBoostRankingEvent;
  boost?: RankingEventWithBoostBoost;
}

export type RankingYellBoostId = number | null;

export type RankingYellBoostCreatedAt = Instant | null;

export type RankingYellBoostUpdatedAt = Instant | null;

export type RankingYellBoostStartAt = Instant | null;

export type RankingYellBoostEndAt = Instant | null;

export type RankingYellBoostBoostRatio = number | null;

export interface RankingYellBoost {
  id?: RankingYellBoostId;
  createdAt: RankingYellBoostCreatedAt;
  updatedAt: RankingYellBoostUpdatedAt;
  startAt?: RankingYellBoostStartAt;
  endAt?: RankingYellBoostEndAt;
  boostRatio?: RankingYellBoostBoostRatio;
}

export interface RegisterCardRequest {
  cardName: string;
  token: string;
}

export type SampleFileThumbnailUri = string | null;

export interface SampleFile {
  name: string;
  objectUri: string;
  thumbnailUri?: SampleFileThumbnailUri;
  fileType: string;
  size?: number;
  duration?: number;
}

export type SaveFanmeCustomerRequestBuilding = string | null;

export interface SaveFanmeCustomerRequest {
  firstName: string;
  lastName: string;
  firstNameKana: string;
  lastNameKana: string;
  postalCode: string;
  prefecture: string;
  city: string;
  street: string;
  building?: SaveFanmeCustomerRequestBuilding;
  phoneNumber: string;
}

export interface SendEmailRequest {
  transactionId?: number;
}

export type ShopDescription = string | null;

export type ShopHeaderImageUri = string | null;

export type ShopMessage = string | null;

export interface Shop {
  id?: number;
  tenant: string;
  creatorName: string;
  creatorIconUri: string;
  creatorUid: string;
  creatorAccountIdentity: string;
  name: string;
  description?: ShopDescription;
  headerImageUri?: ShopHeaderImageUri;
  message?: ShopMessage;
  marginRate?: number;
  isOpen?: boolean;
  limitation: ShopLimitation;
  open?: boolean;
}

export type Shop1Id = number | null;

export type Shop1CreatedAt = Instant | null;

export type Shop1UpdatedAt = Instant | null;

/**
 * @maxLength 50
 */
export type Shop1CreatorUid = string | null;

/**
 * @maxLength 500
 */
export type Shop1Description = string | null;

export type Shop1HeaderImageUri = string | null;

export interface Shop1 {
  id?: Shop1Id;
  createdAt: Shop1CreatedAt;
  updatedAt: Shop1UpdatedAt;
  /** @maxLength 50 */
  name: string;
  tenant: string;
  /** @maxLength 50 */
  creatorUid: Shop1CreatorUid;
  /** @maxLength 500 */
  description?: Shop1Description;
  headerImageUri?: Shop1HeaderImageUri;
  /**
   * @maxLength 100
   * @pattern \S
   */
  message: string;
  /** @minimum 0 */
  marginRate: number;
  /** @minimum 0 */
  tipMarginRate: number;
  isOpen: boolean;
  open?: boolean;
}

export interface ShopData {
  shop: Shop;
}

export type ShopLimitationId = number | null;

export type ShopLimitationCreatedAt = Instant | null;

export type ShopLimitationUpdatedAt = Instant | null;

export interface ShopLimitation {
  id?: ShopLimitationId;
  createdAt: ShopLimitationCreatedAt;
  updatedAt: ShopLimitationUpdatedAt;
  fileCapacity: number;
  fileQuantity: number;
  isChekiExhibitable: boolean;
  chekiExhibitable?: boolean;
}

export type ShopResponseBodyData = ShopData | null;

export type ShopResponseBodyErrors = ErrorObject[] | null;

export interface ShopResponseBody {
  data: ShopResponseBodyData;
  errors?: ShopResponseBodyErrors;
}

export interface SingleOrderData {
  order: OrderResult;
}

export type SingleOrderResponseBodyData = SingleOrderData | null;

export type SingleOrderResponseBodyErrors = ErrorObject[] | null;

export interface SingleOrderResponseBody {
  data: SingleOrderResponseBodyData;
  errors?: SingleOrderResponseBodyErrors;
}

export interface SortItem {
  id?: number;
  sortOrder?: number;
}

export interface SortItemsRequest {
  items: SortItem[];
}

export interface SuggestAddressData {
  suggestedAddress: SuggestedAddressEntity;
}

export type SuggestAddressResponseBodyData = SuggestAddressData | null;

export type SuggestAddressResponseBodyErrors = ErrorObject[] | null;

export interface SuggestAddressResponseBody {
  data?: SuggestAddressResponseBodyData;
  errors?: SuggestAddressResponseBodyErrors;
}

export interface SuggestedAddressEntity {
  prefecture: string;
  city: string;
  street: string;
}

export interface TipLimitData {
  tipLimit: TipUpperLimit;
}

export type TipLimitResponseBodyData = TipLimitData | null;

export type TipLimitResponseBodyErrors = ErrorObject[] | null;

export interface TipLimitResponseBody {
  data: TipLimitResponseBodyData;
  errors?: TipLimitResponseBodyErrors;
}

export interface TipUpperLimit {
  amount?: number;
}

export interface UpdateCardRequest {
  cardSequence?: number;
  cardName: string;
  cardHolderName: string;
  expire: string;
}

export type UpdateCartItemRequestQuantity = number | null;

export type UpdateCartItemRequestPurchaserComment = string | null;

export interface UpdateCartItemRequest {
  quantity?: UpdateCartItemRequestQuantity;
  purchaserComment?: UpdateCartItemRequestPurchaserComment;
}

export type UpdateDigitalGachaItemRequestDescription = string | null;

export type UpdateDigitalGachaItemRequestSamples = DigitalGachaSampleFileForUpdate[] | null;

export type UpdateDigitalGachaItemRequestBenefits = DigitalGachaBenefitForUpdate[] | null;

export type UpdateDigitalGachaItemRequestTags = string[] | null;

export type UpdateDigitalGachaItemRequestItemOption = ItemOption | null;

export interface UpdateDigitalGachaItemRequest {
  name: string;
  description?: UpdateDigitalGachaItemRequestDescription;
  thumbnailUri: string;
  thumbnailFrom?: number;
  thumbnailBlurLevel?: number;
  thumbnailWatermarkLevel?: number;
  price?: number;
  available?: boolean;
  itemFiles: DigitalGachaFileForUpdate[];
  samples?: UpdateDigitalGachaItemRequestSamples;
  benefits?: UpdateDigitalGachaItemRequestBenefits;
  tags?: UpdateDigitalGachaItemRequestTags;
  itemOption?: UpdateDigitalGachaItemRequestItemOption;
}

export type UpdateOrderRequestTransactionId = number | null;

export interface UpdateOrderRequest {
  transactionId?: UpdateOrderRequestTransactionId;
  checkoutId?: number;
  status: string;
}

export type UpdateShopRequestDescription = string | null;

export type UpdateShopRequestHeaderImageUri = string | null;

export type UpdateShopRequestMessage = string | null;

export interface UpdateShopRequest {
  name: string;
  description?: UpdateShopRequestDescription;
  headerImageUri?: UpdateShopRequestHeaderImageUri;
  message?: UpdateShopRequestMessage;
}

export type UpdateStatusRequestComment = string | null;

export type UpdateStatusRequestAuditedUserUid = string | null;

export interface UpdateStatusRequest {
  status?: number;
  comment?: UpdateStatusRequestComment;
  auditedUserUid?: UpdateStatusRequestAuditedUserUid;
}

export type UserId = number | null;

export type UserCreatedAt = Instant | null;

export type UserUpdatedAt = Instant | null;

/**
 * @maxLength 255
 */
export type UserIcon = string | null;

/**
 * @maxLength 255
 */
export type UserName = string | null;

/**
 * @maxLength 255
 */
export type UserGender = string | null;

export type UserBirthday = Instant | null;

export type UserBirthdayConfirmed = boolean | null;

/**
 * @maxLength 255
 */
export type UserAccountIdentity = string | null;

export type UserPublic = boolean | null;

export type UserAllowPublicSharing = boolean | null;

/**
 * @maxLength 255
 */
export type UserUid = string | null;

export type UserDeletedAt = Instant | null;

export type UserFilledProfile = boolean | null;

export type UserPurpose = number | null;

export type UserIsBirthdayWeek = number | null;

export interface User {
  id?: UserId;
  createdAt: UserCreatedAt;
  updatedAt: UserUpdatedAt;
  /** @maxLength 255 */
  icon?: UserIcon;
  /** @maxLength 255 */
  name: UserName;
  /** @maxLength 255 */
  gender: UserGender;
  birthday: UserBirthday;
  birthdayConfirmed: UserBirthdayConfirmed;
  /** @maxLength 255 */
  accountIdentity: UserAccountIdentity;
  public: UserPublic;
  allowPublicSharing?: UserAllowPublicSharing;
  /** @maxLength 255 */
  uid?: UserUid;
  deletedAt?: UserDeletedAt;
  filledProfile: UserFilledProfile;
  purpose: UserPurpose;
  isBirthdayWeek?: UserIsBirthdayWeek;
  birthdayWeek?: number;
  iconUrl: string;
}

export interface UsersData {
  users: User[];
}

export type UsersResponseBodyData = UsersData | null;

export type UsersResponseBodyErrors = ErrorObject[] | null;

export interface UsersResponseBody {
  data: UsersResponseBodyData;
  errors?: UsersResponseBodyErrors;
}

export type GetAgencySalesParams = {
  from?: string | null;
  to?: string | null;
};

export type GetCurrentUserItemsParams = {
  available?: boolean | null;
  tag?: string | null;
};

export type GetCurrentUserItemParams = {
  includeDeleted?: string | null;
};

export type GetPurchasedItemsParams = {
  includeTip?: boolean | null;
};

export type GetItemsParams = {
  available?: boolean | null;
  tag?: string | null;
};

export type GmoWebhookBody = {
  ShopID: string;
  ShopPass: string;
  AccessID: string;
  AccessPass: string;
  OrderID: string;
  Status: string;
  Amount: string;
  Tax: string;
  PayType: string;
};
