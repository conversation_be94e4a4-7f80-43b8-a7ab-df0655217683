import { useState, useEffect, useCallback, useMemo } from 'react';
import { useForm } from 'react-hook-form';
import { useParams } from 'next/navigation';
import { useExhibitsStore } from '@/store/useExhibit';
import { MAX_UPLOAD_GACHA_BENEFITS } from '@/consts/inputLength';
import {
  getAvailableConditions,
  clearInvalidSubsequentConditions,
  BenefitItem as BenefitItemType,
  FormValues,
} from '@/utils/gacha-benefit-utils';
import type { ExhibitGachaItem } from '@/types/exhibitItem';
import { ConditionType, CONDITION_TYPE, type GachaBenefit, type GachaBenefitFile } from '@/types/gacha';
import { ItemFiles, MediaType } from '@/types/shopItem';

const DEFAULT_CONDITION = CONDITION_TYPE.TIMES_10;

export const useGachaBenefit = () => {
  const { control, setValue, getValues } = useForm<FormValues>({
    defaultValues: {
      benefits: {
        [DEFAULT_CONDITION]: { conditionType: DEFAULT_CONDITION, description: '' },
      },
    },
  });

  const useExhibits = useExhibitsStore();
  const { exhibits, setBenefits } = useExhibits;
  const params = useParams();
  const itemId = params.id as string;

  const exhibitItem = useMemo(() => exhibits.find((e) => e.itemId === itemId), [exhibits, itemId]);

  const { benefits = [], isDuplicated = true } = (exhibitItem as ExhibitGachaItem) ?? {};

  const [benefitItems, setBenefitItems] = useState<BenefitItemType[]>([
    { id: 0, conditionType: DEFAULT_CONDITION, description: '', benefitFiles: [], uploadProgress: 0 },
  ]);

  const [isCompBenefitEnabled] = useState(true);
  const [textLengths, setTextLengths] = useState<{ [key: string]: number }>({ '0': 0 });

  const createEmptyBenefit = useCallback(
    (conditionType = DEFAULT_CONDITION): GachaBenefit => ({
      id: 0,
      description: '',
      conditionType,
      benefitFiles: [],
    }),
    [],
  );

  const updateStoreBenefits = useCallback(
    (benefitId: number, updatedData: Partial<GachaBenefit>, shouldCheck = true) => {
      const existingBenefits = benefits || [];
      const existingBenefitIndex = existingBenefits.findIndex((_, index) => index === benefitId);

      let updatedBenefits: GachaBenefit[];

      if (existingBenefitIndex >= 0) {
        updatedBenefits = existingBenefits.map((benefit, index) => {
          if (index === existingBenefitIndex) {
            return { ...benefit, ...updatedData };
          }
          return benefit;
        });
      } else {
        const newBenefits = [...existingBenefits];

        while (newBenefits.length <= benefitId) {
          newBenefits.push(createEmptyBenefit());
        }

        const currentBenefitItem = benefitItems.find((item) => item.id === benefitId);
        const newBenefit: GachaBenefit = {
          id: 0,
          description: currentBenefitItem?.description || '',
          conditionType: currentBenefitItem?.conditionType || DEFAULT_CONDITION,
          benefitFiles: [],
          ...updatedData,
        };

        newBenefits[benefitId] = newBenefit;
        updatedBenefits = newBenefits;
      }

      if (!shouldCheck) {
        setBenefits(itemId, updatedBenefits);
        return;
      }

      const currentBenefitItem = benefitItems.find((item) => item.id === benefitId);
      const hasFiles = currentBenefitItem?.benefitFiles && currentBenefitItem.benefitFiles.length > 0;

      if (hasFiles || updatedData.benefitFiles?.length) {
        setBenefits(itemId, updatedBenefits);
      }
    },
    [benefits, benefitItems, itemId, setBenefits, createEmptyBenefit],
  );

  useEffect(() => {
    if (benefits && benefits.length > 0) {
      const initialBenefitItems: BenefitItemType[] = benefits.map((benefit, index) => ({
        id: index,
        conditionType: benefit.conditionType,
        description: benefit.description || '',
        benefitFiles: benefit.benefitFiles.map((file) => ({
          ...file,
          src: file.src || '',
          type: file.type,
          duration: file.duration,
          thumbnail: file.thumbnail,
          conditionType: file.conditionType,
          sortOrder: file.sortOrder,
        })),
        uploadProgress: 0,
      }));

      if (initialBenefitItems.length < MAX_UPLOAD_GACHA_BENEFITS) {
        const nextId = initialBenefitItems.length;
        initialBenefitItems.push({
          id: nextId,
          conditionType: DEFAULT_CONDITION,
          description: '',
          benefitFiles: [],
          uploadProgress: 0,
        });
      }

      setBenefitItems(initialBenefitItems);

      const initialTextLengths: { [key: string]: number } = {};
      initialBenefitItems.forEach((item) => {
        initialTextLengths[item.id || 0] = item.description?.length || 0;
      });
      setTextLengths(initialTextLengths);

      const formValues: FormValues['benefits'] = {};
      initialBenefitItems.forEach((item) => {
        formValues[item.id || 0] = {
          conditionType: item.conditionType,
          description: item.description || '',
        };
      });
      setValue('benefits', formValues);
    }
  }, [benefits, setValue]);

  const handleConditionChange = useCallback(
    (benefitId: number, selectedCondition: ConditionType) => {
      setValue(`benefits.${benefitId}.conditionType`, selectedCondition);

      setBenefitItems((prev) => {
        const previousItem = prev.find((item) => item.id === benefitId);
        const previousCondition = previousItem?.conditionType;

        const updatedItems = prev.map((item) =>
          item.id === benefitId ? { ...item, conditionType: selectedCondition } : item,
        );

        return clearInvalidSubsequentConditions(
          updatedItems,
          benefitId,
          selectedCondition,
          setValue,
          previousCondition,
        );
      });

      updateStoreBenefits(benefitId, {
        conditionType: selectedCondition,
        benefitFiles:
          benefits?.[benefitId]?.benefitFiles?.map((file) => ({
            ...file,
            conditionType: selectedCondition,
          })) || [],
      });
    },
    [setValue, updateStoreBenefits, benefits],
  );

  const handleDescriptionChange = useCallback(
    (benefitId: number, description: string) => {
      setValue(`benefits.${benefitId}.description`, description);
      setBenefitItems((prev) => prev.map((item) => (item.id === benefitId ? { ...item, description } : item)));
      setTextLengths((prev) => ({ ...prev, [benefitId]: description.length }));

      updateStoreBenefits(benefitId, { description });
    },
    [setValue, updateStoreBenefits],
  );

  const handleFileUpload = useCallback(
    (benefitId: number, files: ItemFiles) => {
      const currentBenefitItem = benefitItems.find((item) => item.id === benefitId);
      const conditionType = currentBenefitItem?.conditionType || DEFAULT_CONDITION;

      // 创建最终的文件数据（直接使用传入的files，因为UploadFile组件已经处理了上传）
      const gachaBenefitFiles: GachaBenefitFile[] = files.map((file) => ({
        id: file.id,
        title: file.title,
        src: file.src,
        type: (file.type as MediaType) || 'image',
        size: file.size,
        duration: file.duration,
        thumbnail: file.preSignedThumbnailUrl,
        conditionType,
        sortOrder: file.sortIndex,
        isLoading: false, // 文件已经上传完成
      }));

      // 同步到store，设置id为1（表示已上传）
      updateStoreBenefits(
        benefitId,
        {
          id: 1, // 上传成功设置id为1
          description: currentBenefitItem?.description || '',
          conditionType,
          benefitFiles: gachaBenefitFiles,
        },
        false,
      );

      // 更新local状态
      setBenefitItems((prev) => {
        const updatedItems: BenefitItemType[] = prev.map((item) =>
          item.id === benefitId
            ? {
                ...item,
                benefitFiles: gachaBenefitFiles.map((file) => ({
                  ...file,
                  src: file.src,
                  type: (file.type as MediaType) || 'image',
                  duration: file.duration,
                  thumbnail: file.preSignedThumbnailUrl,
                  conditionType,
                  sortOrder: file.sortOrder,
                })),
              }
            : item,
        );

        const prevItem = prev.find((item) => item.id === benefitId);
        const shouldAddNewBenefit =
          updatedItems.length < MAX_UPLOAD_GACHA_BENEFITS &&
          prevItem &&
          (!prevItem.benefitFiles || prevItem.benefitFiles.length === 0) &&
          files &&
          files.length > 0;

        if (shouldAddNewBenefit) {
          const newId = updatedItems.length;
          updatedItems.push({
            id: newId,
            conditionType: DEFAULT_CONDITION,
            description: '',
            benefitFiles: [],
            uploadProgress: 0,
          });
        }

        return updatedItems;
      });
    },
    [benefitItems, updateStoreBenefits],
  );

  const handleFileDelete = useCallback(
    (benefitId: number) => {
      setBenefitItems((prev) => prev.map((item) => (item.id === benefitId ? { ...item, benefitFiles: [] } : item)));

      // Sync the file deletion to the store
      updateStoreBenefits(benefitId, { benefitFiles: [] }, false);
    },
    [updateStoreBenefits],
  );

  const handleUploadProgress = useCallback((benefitId: number, progress: number) => {
    setBenefitItems((prev) =>
      prev.map((item) => (item.id === benefitId ? { ...item, uploadProgress: progress } : item)),
    );
  }, []);

  const deleteBenefit = useCallback(
    (benefitId: number) => {
      if (benefitItems.length <= 1) return;

      const remainingItems = benefitItems.filter((item) => item.id !== benefitId);
      const reorderedItems = remainingItems.map((item, index) => ({
        ...item,
        id: index,
      }));

      setBenefitItems(reorderedItems);

      const newFormValues: FormValues['benefits'] = {};
      const newTextLengths: { [key: string]: number } = {};

      reorderedItems.forEach((item, index) => {
        const newId = index;
        newFormValues[newId] = {
          conditionType: item.conditionType,
          description: item.description || '',
        };
        newTextLengths[newId] = item.description?.length || 0;
      });

      setValue('benefits', newFormValues);
      setTextLengths(newTextLengths);
    },
    [benefitItems, setValue],
  );

  useEffect(() => {
    benefitItems.forEach((item) => {
      if (!getValues(`benefits.${item.id}`)) {
        setValue(`benefits.${item.id}`, {
          conditionType: item.conditionType,
          description: item.description || '',
        });
        setTextLengths((prev) => ({ ...prev, [item.id || 0]: item.description?.length || 0 }));
      }
    });
  }, [benefitItems, setValue, getValues]);

  useEffect(() => {
    if (isDuplicated === true) {
      setBenefitItems((prev) => {
        const needsUpdate = prev.some((item) => item.conditionType === CONDITION_TYPE.COMPLETED);

        if (needsUpdate) {
          return prev.map((item) => {
            if (item.conditionType === CONDITION_TYPE.COMPLETED) {
              const resetCondition = DEFAULT_CONDITION;
              setValue(`benefits.${item.id}.conditionType`, resetCondition);
              return { ...item, conditionType: resetCondition };
            }
            return item;
          });
        }

        return prev;
      });
    }
  }, [isDuplicated, setValue]);

  return {
    // State
    benefitItems,
    textLengths,
    isCompBenefitEnabled,
    isDuplicated,

    // Form
    control,

    // Handlers
    handleConditionChange,
    handleDescriptionChange,
    handleFileUpload,
    handleFileDelete,
    handleUploadProgress,
    deleteBenefit,

    // Utils
    getAvailableConditions: (benefitId: number) =>
      getAvailableConditions(benefitId, benefitItems, isCompBenefitEnabled, isDuplicated),
  };
};
